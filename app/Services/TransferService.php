<?php

namespace App\Services;

use App\Models\FantasyTeam;
use App\Models\FantasyTeamGameweek;
use App\Models\FantasyTransfer;
use App\Models\Gameweek;
use Illuminate\Support\Facades\DB;

class TransferService
{
    protected SquadManagementService $squadService;

    public function __construct(SquadManagementService $squadService)
    {
        $this->squadService = $squadService;
    }

    /**
     * Process transfers for a fantasy team
     */
    public function processTransfers(
        FantasyTeam $fantasyTeam,
        Gameweek $currentGameweek,
        array $validatedData,
        array $newSquadPlayerIds,
        array $playersAdded,
        array $playersRemoved,
        float $newSquadValue
    ): void {
        DB::transaction(function () use (
            $fantasyTeam,
            $currentGameweek,
            $validatedData,
            $newSquadPlayerIds,
            $playersAdded,
            $playersRemoved,
            $newSquadValue
        ) {
            // Calculate transfer costs
            $freeTransfers = $currentGameweek->free_transfers ?? 1;
            $transfersMade = count($playersAdded);
            $freeTransfersUsed = min($transfersMade, $freeTransfers);
            $paidTransfers = max(0, $transfersMade - $freeTransfers);
            $transferCostPenalty = $paidTransfers * 4; // 4 points per transfer above free limit
            $pointsDeducted = $transferCostPenalty;

            // Update fantasy team budget
            $fantasyTeam->updateBudget($newSquadValue);

            // Use squad management service to handle the simplified transfer logic
            $this->squadService->processSquadTransfers(
                $fantasyTeam,
                $currentGameweek,
                $newSquadPlayerIds,
                $playersAdded,
                $playersRemoved,
                $validatedData
            );

            // Record individual transfers
            $this->recordTransfers(
                $fantasyTeam,
                $currentGameweek,
                $playersAdded,
                $playersRemoved,
                $freeTransfersUsed,
                $paidTransfers
            );

            // Create or update fantasy team gameweek record
            $this->updateFantasyTeamGameweek(
                $fantasyTeam,
                $currentGameweek,
                $transferCostPenalty,
                $transfersMade,
                $freeTransfersUsed,
                $pointsDeducted
            );
        });
    }

    /**
     * Record individual transfers in the transfers table
     */
    protected function recordTransfers(
        FantasyTeam $fantasyTeam,
        Gameweek $currentGameweek,
        array $playersAdded,
        array $playersRemoved,
        int $freeTransfersUsed,
        int $paidTransfers
    ): void {
        $transferIndex = 0;
        $maxTransfers = max(count($playersAdded), count($playersRemoved));

        for ($i = 0; $i < $maxTransfers; $i++) {
            $playerInId = $playersAdded[$i] ?? null;
            $playerOutId = $playersRemoved[$i] ?? null;

            // Determine if this is a free transfer
            $isFreeTransfer = $transferIndex < $freeTransfersUsed;
            $transferCost = $isFreeTransfer ? 0 : 4;

            // Determine transfer type
            $transferType = 'swap';
            if ($playerInId && ! $playerOutId) {
                $transferType = 'in';
            } elseif (! $playerInId && $playerOutId) {
                $transferType = 'out';
            }

            FantasyTransfer::create([
                'fantasy_team_id' => $fantasyTeam->id,
                'gameweek_id' => $currentGameweek->id,
                'player_in_id' => $playerInId,
                'player_out_id' => $playerOutId,
                'transfer_cost' => $transferCost,
                'is_free_transfer' => $isFreeTransfer,
                'transfer_type' => $transferType,
            ]);

            $transferIndex++;
        }
    }

    /**
     * Create or update fantasy team gameweek record
     */
    protected function updateFantasyTeamGameweek(
        FantasyTeam $fantasyTeam,
        Gameweek $currentGameweek,
        float $transferCostPenalty,
        int $transfersMade,
        int $freeTransfersUsed,
        int $pointsDeducted
    ): void {
        FantasyTeamGameweek::updateOrCreate(
            [
                'fantasy_team_id' => $fantasyTeam->id,
                'gameweek_id' => $currentGameweek->id,
            ],
            [
                'transfer_cost_penalty' => $transferCostPenalty,
                'transfers_made' => $transfersMade,
                'free_transfers_used' => $freeTransfersUsed,
                'points_deducted' => $pointsDeducted,
                // total_points will be updated later when gameweek results are calculated
            ]
        );
    }

    /**
     * Get transfer history for a fantasy team
     */
    public function getTransferHistory(FantasyTeam $fantasyTeam, ?Gameweek $gameweek = null): array
    {
        $query = FantasyTransfer::where('fantasy_team_id', $fantasyTeam->id)
            ->with(['playerIn', 'playerOut', 'gameweek']);

        if ($gameweek) {
            $query->where('gameweek_id', $gameweek->id);
        }

        return $query->orderBy('created_at', 'desc')->get()->toArray();
    }

    /**
     * Get fantasy team gameweek stats
     */
    public function getFantasyTeamGameweekStats(FantasyTeam $fantasyTeam, Gameweek $gameweek): ?FantasyTeamGameweek
    {
        return FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $gameweek->id)
            ->first();
    }

    /**
     * Calculate remaining free transfers for a gameweek
     */
    public function getRemainingFreeTransfers(FantasyTeam $fantasyTeam, Gameweek $gameweek): int
    {
        $gameweekStats = $this->getFantasyTeamGameweekStats($fantasyTeam, $gameweek);
        $freeTransfersUsed = $gameweekStats ? $gameweekStats->free_transfers_used : 0;
        $totalFreeTransfers = $gameweek->free_transfers ?? 1;

        return max(0, $totalFreeTransfers - $freeTransfersUsed);
    }
}
