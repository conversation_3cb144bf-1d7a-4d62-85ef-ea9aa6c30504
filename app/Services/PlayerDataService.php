<?php

namespace App\Services;

use App\Models\FantasyPlayer;
use App\Models\Player;

class PlayerDataService
{
    /**
     * Normalize position names to match frontend expectations
     */
    public function normalizePosition(string $position): string
    {
        $positionMap = [
            'Goalkeeper' => 'GK',
            'GK' => 'GK',
            'Defender' => 'DEF',
            'DEF' => 'DEF',
            'CB' => 'DEF',
            'LB' => 'DEF',
            'RB' => 'DEF',
            'LWB' => 'DEF',
            'RWB' => 'DEF',
            'Midfielder' => 'MID',
            'MID' => 'MID',
            'CM' => 'MID',
            'CDM' => 'MID',
            'CAM' => 'MID',
            'LM' => 'MID',
            'RM' => 'MID',
            'Forward' => 'FWD',
            'FWD' => 'FWD',
            'ST' => 'FWD',
            'CF' => 'FWD',
            'LW' => 'FWD',
            'RW' => 'FWD',
        ];

        return $positionMap[$position] ?? 'MID';
    }

    /**
     * Transform fantasy player data for frontend consumption
     */
    public function transformFantasyPlayerData(FantasyPlayer $fantasyPlayer, $currentGameweek): array
    {
        $player = $fantasyPlayer->player;
        $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;

        // Get the first team (assuming current team is the first one)
        $currentTeam = $player->teams->first();

        return [
            'id' => $player->id,
            'name' => $player->name,
            'position' => $this->normalizePosition($player->position->value),
            'price' => $marketValue, // Use raw database value
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
            'is_captain' => $fantasyPlayer->is_captain,
            'is_vice_captain' => $fantasyPlayer->is_vice_captain,
            'fantasy_player_id' => $fantasyPlayer->id,
        ];
    }

    /**
     * Transform regular player data for frontend consumption
     */
    public function transformPlayerData(Player $player, $currentGameweek): array
    {
        $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;

        // Get the first team (assuming current team is the first one)
        $currentTeam = $player->teams->first();

        return [
            'id' => $player->id,
            'name' => $player->name,
            'position' => $this->normalizePosition($player->position->value),
            'price' => $marketValue, // Use raw database value
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
        ];
    }

    /**
     * Get squad players data for a fantasy team and gameweek
     */
    public function getSquadPlayersData($fantasyTeamId, $currentGameweek): \Illuminate\Support\Collection
    {
        return FantasyPlayer::with(['player.teams', 'player.marketValues'])
            ->where('fantasy_team_id', $fantasyTeamId)
            ->where('gameweek_id', $currentGameweek->id)
            ->get()
            ->map(function ($fantasyPlayer) use ($currentGameweek) {
                return $this->transformFantasyPlayerData($fantasyPlayer, $currentGameweek);
            });
    }

    /**
     * Get available players data (excluding current squad players)
     */
    public function getAvailablePlayersData(array $excludePlayerIds, $currentGameweek, $competition = null): \Illuminate\Support\Collection
    {
        $query = Player::with(['teams', 'marketValues' => function ($query) use ($currentGameweek) {
            $query->where('gameweek_id', $currentGameweek->id);
        }])
            ->whereNotIn('id', $excludePlayerIds);

        // Filter by competition if provided
        if ($competition) {
            $query->whereHas('teams', function ($query) use ($competition) {
                // Teams are related to seasons, and seasons are related to competitions
                $query->whereHas('seasons', function ($q) use ($competition) {
                    $q->where('seasons.competition_id', $competition->id);
                });
            });
        }

        return $query->get()
            ->map(function ($player) use ($currentGameweek) {
                return $this->transformPlayerData($player, $currentGameweek);
            });
    }

    /**
     * Get substitute priority based on player position
     */
    public function getSubstitutePriority(string $position): int
    {
        $normalizedPosition = $this->normalizePosition($position);
        
        return match ($normalizedPosition) {
            'GK' => 1,
            'DEF' => 2,
            'MID' => 3,
            'FWD' => 4,
            default => 1
        };
    }
}
