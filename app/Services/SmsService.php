<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class SmsService
{
    protected $apiUrl;
    protected $apiKey;
    protected $senderId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.api_url');
        $this->apiKey = config('services.sms.api_key');
        $this->senderId = config('services.sms.sender_id', 'FantasyApp');
    }

    /**
     * Send OTP code via SMS
     */
    public function sendOtp(string $phone, string $code): bool
    {
        $message = "Your Fantasy Football verification code is: {$code}. This code will expire in 10 minutes.";
        
        return $this->sendSms($phone, $message);
    }

    /**
     * Send SMS message
     */
    public function sendSms(string $phone, string $message): bool
    {
        try {
            // For development/testing, just log the SMS
            if (app()->environment('local')) {
                Log::info("SMS to {$phone}: {$message}");
                return true;
            }

            // In production, integrate with your SMS provider
            // Example implementation for a generic SMS API:
            $response = Http::post($this->apiUrl, [
                'api_key' => $this->apiKey,
                'sender_id' => $this->senderId,
                'phone' => $phone,
                'message' => $message,
            ]);

            if ($response->successful()) {
                Log::info("SMS sent successfully to {$phone}");
                return true;
            } else {
                Log::error("Failed to send SMS to {$phone}: " . $response->body());
                return false;
            }
        } catch (\Exception $e) {
            Log::error("SMS service error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Format phone number for SMS sending
     */
    public function formatPhoneNumber(string $phone): string
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if not present (assuming default country)
        if (!str_starts_with($phone, '212')) { // Morocco country code
            $phone = '212' . ltrim($phone, '0');
        }
        
        return $phone;
    }
}
