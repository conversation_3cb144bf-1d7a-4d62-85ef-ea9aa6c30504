<?php

namespace App\Services;

use App\Models\Competition;
use App\Models\Tenant;
use Illuminate\Http\Request;

class FantasyContextService
{
    /**
     * Get the complete fantasy context (competition, season, phase, gameweek)
     * 
     * @param Request $request
     * @return array|null Returns array with context data or null if invalid
     */
    public function getFantasyContext(Request $request): ?array
    {
        $tenant = Tenant::current();
        if (!$tenant) {
            return null;
        }

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');
        if (!$currentCompetitionId) {
            return null;
        }

        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (!$competition) {
            return null;
        }

        $currentSeason = $competition->currentSeason;
        if (!$currentSeason) {
            return null;
        }

        $currentSeasonPhase = $currentSeason->currentSeasonPhase;
        if (!$currentSeasonPhase) {
            return null;
        }

        $currentGameweek = $currentSeasonPhase->currentGameweek;
        if (!$currentGameweek) {
            return null;
        }

        return [
            'tenant' => $tenant,
            'competition' => $competition,
            'currentSeason' => $currentSeason,
            'currentSeasonPhase' => $currentSeasonPhase,
            'currentGameweek' => $currentGameweek,
        ];
    }

    /**
     * Get fantasy context with error messages for redirects
     * 
     * @param Request $request
     * @return array Returns ['success' => bool, 'data' => array|null, 'error' => string|null]
     */
    public function getFantasyContextWithErrors(Request $request): array
    {
        $tenant = Tenant::current();
        if (!$tenant) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No tenant context available.'
            ];
        }

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');
        if (!$currentCompetitionId) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No competition selected.'
            ];
        }

        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (!$competition) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active competition found.'
            ];
        }

        $currentSeason = $competition->currentSeason;
        if (!$currentSeason) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active season found.'
            ];
        }

        $currentSeasonPhase = $currentSeason->currentSeasonPhase;
        if (!$currentSeasonPhase) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active season phase found.'
            ];
        }

        $currentGameweek = $currentSeasonPhase->currentGameweek;
        if (!$currentGameweek) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active gameweek found.'
            ];
        }

        return [
            'success' => true,
            'data' => [
                'tenant' => $tenant,
                'competition' => $competition,
                'currentSeason' => $currentSeason,
                'currentSeasonPhase' => $currentSeasonPhase,
                'currentGameweek' => $currentGameweek,
            ],
            'error' => null
        ];
    }
}
