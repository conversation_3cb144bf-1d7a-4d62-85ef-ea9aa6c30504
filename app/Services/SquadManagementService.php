<?php

namespace App\Services;

use App\Models\FantasyPlayer;
use App\Models\FantasyTeam;
use App\Models\FantasyTeamLineup;
use App\Models\Player;
use Illuminate\Support\Facades\DB;

class SquadManagementService
{
    protected PlayerDataService $playerDataService;
    protected array $pendingLineupInfo = [];

    public function __construct(PlayerDataService $playerDataService)
    {
        $this->playerDataService = $playerDataService;
    }

    /**
     * Process squad transfers (swap player IDs while preserving all other attributes)
     */
    public function processSquadTransfers(FantasyTeam $fantasyTeam, $currentGameweek, array $newSquadPlayerIds, array $playersAdded, array $playersRemoved, array $validatedData): void
    {
        DB::transaction(function () use ($fantasyTeam, $currentGameweek, $newSquadPlayerIds, $playersAdded, $playersRemoved, $validatedData) {
            // Update budget based on squad value changes
            $currentSquadValue = $this->calculateSquadValue($fantasyTeam, $currentGameweek);
            $newSquadValue = $this->calculateNewSquadValue($newSquadPlayerIds, $currentGameweek);
            $fantasyTeam->budget = $fantasyTeam->budget - $newSquadValue;
            $fantasyTeam->save();
            
            // Simple approach: swap player IDs in existing FantasyPlayer records
            // This preserves captain status, vice-captain status, and all other attributes
            $this->swapPlayersInSquad($fantasyTeam, $currentGameweek, $playersAdded, $playersRemoved);
        });
    }

    /**
     * Swap player IDs in existing FantasyPlayer records (preserves all other attributes)
     */
    protected function swapPlayersInSquad(FantasyTeam $fantasyTeam, $currentGameweek, array $playersAdded, array $playersRemoved): void
    {
        // Get all current fantasy players for this team and gameweek
        $fantasyPlayers = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->get()
            ->keyBy('player_id');

        // For each player being removed, find the corresponding player being added and swap IDs
        foreach ($playersRemoved as $index => $oldPlayerId) {
            $newPlayerId = $playersAdded[$index] ?? null;
            
            if ($newPlayerId && isset($fantasyPlayers[$oldPlayerId])) {
                $fantasyPlayer = $fantasyPlayers[$oldPlayerId];
                
                // Get the new player's market value for purchase price
                $newPlayer = Player::find($newPlayerId);
                $purchasePrice = $newPlayer ? $newPlayer->getMarketValueForGameweek($currentGameweek->id) : 0;
                
                // Simply update the player_id and purchase_price, preserving all other attributes
                $fantasyPlayer->update([
                    'player_id' => $newPlayerId,
                    'purchase_price' => $purchasePrice ?? 0,
                ]);
            }
        }
    }

    /**
     * Calculate current squad value
     */
    protected function calculateSquadValue(FantasyTeam $fantasyTeam, $currentGameweek): float
    {
        return FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->sum('purchase_price') ?? 0;
    }

    /**
     * Calculate new squad value based on player IDs
     */
    protected function calculateNewSquadValue(array $playerIds, $currentGameweek): float
    {
        $total = 0;
        $players = Player::whereIn('id', $playerIds)
            ->with(['marketValues' => function ($query) use ($currentGameweek) {
                $query->where('gameweek_id', $currentGameweek->id);
            }])
            ->get();

        foreach ($players as $player) {
            $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;
            $total += $marketValue;
        }

        return $total;
    }

    /**
     * Remove players from squad
     */
    protected function removePlayersFromSquad(FantasyTeam $fantasyTeam, $currentGameweek, array $playersRemoved): void
    {
        if (empty($playersRemoved)) {
            return;
        }

        FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->whereHas('player', function ($query) use ($playersRemoved) {
                $query->whereIn('id', $playersRemoved);
            })
            ->delete();
    }

    /**
     * Add new players to squad
     */
    protected function addPlayersToSquad(FantasyTeam $fantasyTeam, $currentGameweek, array $playersAdded, array $validatedData): void
    {
        foreach ($playersAdded as $playerId) {
            // Get the current market value for this player in this gameweek
            $player = Player::find($playerId);
            $purchasePrice = $player ? $player->getMarketValueForGameweek($currentGameweek->id) : 0;
            
            FantasyPlayer::create([
                'fantasy_team_id' => $fantasyTeam->id,
                'player_id' => $playerId,
                'gameweek_id' => $currentGameweek->id,
                'is_captain' => $playerId == ($validatedData['captain_id'] ?? null),
                'is_vice_captain' => $playerId == ($validatedData['vice_captain_id'] ?? null),
                'purchase_price' => $purchasePrice ?? 0,
            ]);
        }
    }

    /**
     * Update captain and vice-captain status for all squad players
     */
    public function updateCaptainStatus(FantasyTeam $fantasyTeam, $currentGameweek, array $validatedData): void
    {
        // Reset all captain flags first
        FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->update([
                'is_captain' => false,
                'is_vice_captain' => false,
            ]);

        // Set new captain
        if (isset($validatedData['captain_id'])) {
            FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
                ->where('gameweek_id', $currentGameweek->id)
                ->where('player_id', $validatedData['captain_id'])
                ->update(['is_captain' => true]);
        }

        // Set new vice captain
        if (isset($validatedData['vice_captain_id'])) {
            FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
                ->where('gameweek_id', $currentGameweek->id)
                ->where('player_id', $validatedData['vice_captain_id'])
                ->update(['is_vice_captain' => true]);
        }
    }

    /**
     * Update squad lineup for transfers - preserves existing lineup structure
     */
    public function updateSquadLineupForTransfers(FantasyTeam $fantasyTeam, $currentGameweek, array $playersAdded, array $playersRemoved): void
    {
        // First, capture the lineup information for players being removed BEFORE any deletions
        $removedPlayersLineupInfo = [];
        
        foreach ($playersRemoved as $removedPlayerId) {
            $lineupEntry = FantasyTeamLineup::whereHas('fantasyPlayer', function ($query) use ($fantasyTeam, $removedPlayerId) {
                $query->where('fantasy_team_id', $fantasyTeam->id)
                      ->where('player_id', $removedPlayerId);
            })
            ->where('gameweek_id', $currentGameweek->id)
            ->with('fantasyPlayer')
            ->first();
            
            if ($lineupEntry) {
                $removedPlayersLineupInfo[$removedPlayerId] = [
                    'position' => $lineupEntry->position,
                    'sub_priority' => $lineupEntry->sub_priority,
                    'is_captain' => $lineupEntry->fantasyPlayer->is_captain,
                    'is_vice_captain' => $lineupEntry->fantasyPlayer->is_vice_captain,
                ];
            }
        }
        
        // Now we can safely remove lineup entries for transferred out players
        FantasyTeamLineup::whereHas('fantasyPlayer', function ($query) use ($fantasyTeam, $playersRemoved) {
            $query->where('fantasy_team_id', $fantasyTeam->id)
                  ->whereIn('player_id', $playersRemoved);
        })
        ->where('gameweek_id', $currentGameweek->id)
        ->delete();
        
        // Now create lineup entries for new players after they are added to FantasyPlayer table
        // This method will be called after addPlayersToSquad, so we need to defer the lineup creation
        // Store the lineup info for use after players are added
        $this->pendingLineupInfo = $removedPlayersLineupInfo;
    }
    
    /**
     * Create lineup entries for transferred players using stored lineup info
     */
    public function createLineupEntriesForTransfers(FantasyTeam $fantasyTeam, $currentGameweek, array $playersAdded, array $playersRemoved): void
    {
        if (!isset($this->pendingLineupInfo)) {
            return;
        }
        
        // Get the new fantasy players for transferred in players
        $newFantasyPlayers = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->whereIn('player_id', $playersAdded)
            ->get()
            ->keyBy('player_id');

        // Create lineup entries for new players, preserving the positions of removed players
        foreach ($playersAdded as $index => $newPlayerId) {
            $removedPlayerId = $playersRemoved[$index] ?? null;
            $newFantasyPlayer = $newFantasyPlayers[$newPlayerId] ?? null;
            
            if (!$newFantasyPlayer || !$removedPlayerId) {
                continue;
            }

            // Get the lineup info from the removed player
            $removedPlayerLineupInfo = $this->pendingLineupInfo[$removedPlayerId] ?? null;
            
            if ($removedPlayerLineupInfo) {
                // Create new lineup entry with the same position and priority as the removed player
                FantasyTeamLineup::create([
                    'fantasy_player_id' => $newFantasyPlayer->id,
                    'gameweek_id' => $currentGameweek->id,
                    'position' => $removedPlayerLineupInfo['position'],
                    'sub_priority' => $removedPlayerLineupInfo['sub_priority'],
                ]);
            } else {
                // Fallback: add to bench if we can't find the removed player's position
                $player = Player::find($newPlayerId);
                $subPriority = $player ? $this->playerDataService->getSubstitutePriority($player->position->value) : 1;
                
                FantasyTeamLineup::create([
                    'fantasy_player_id' => $newFantasyPlayer->id,
                    'gameweek_id' => $currentGameweek->id,
                    'position' => 'bench',
                    'sub_priority' => $subPriority,
                ]);
            }
        }
        
        // Clear the pending info
        unset($this->pendingLineupInfo);
    }

    /**
     * Update squad lineup (starting XI and bench) - for full lineup changes
     */
    public function updateSquadLineup(FantasyTeam $fantasyTeam, $currentGameweek, array $squadPlayerIds, array $validatedData): void
    {
        // Clear existing lineup for this gameweek
        FantasyTeamLineup::whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
            $query->where('fantasy_team_id', $fantasyTeam->id);
        })
            ->where('gameweek_id', $currentGameweek->id)
            ->delete();

        // Get all fantasy players for this team and gameweek
        $fantasyPlayers = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->get()
            ->keyBy('player_id');

        // Create new lineup entries
        foreach ($squadPlayerIds as $playerId) {
            $fantasyPlayer = $fantasyPlayers[$playerId] ?? null;
            if (!$fantasyPlayer) {
                continue;
            }

            $isStarting = in_array($playerId, $validatedData['lineup_players'] ?? []);
            $position = $isStarting ? 'starting' : 'bench';

            // Determine sub priority for bench players
            $subPriority = 1;
            if (!$isStarting) {
                $player = Player::find($playerId);
                if ($player) {
                    $subPriority = $this->playerDataService->getSubstitutePriority($player->position->value);
                }
            }

            FantasyTeamLineup::create([
                'fantasy_player_id' => $fantasyPlayer->id,
                'gameweek_id' => $currentGameweek->id,
                'position' => $position,
                'sub_priority' => $isStarting ? 0 : $subPriority,
            ]);
        }
    }

    /**
     * Update only the lineup (for team management without transfers)
     */
    public function updateLineupOnly(FantasyTeam $fantasyTeam, $currentGameweek, array $validatedData): void
    {
        DB::transaction(function () use ($fantasyTeam, $currentGameweek, $validatedData) {
            // Get current squad player IDs
            $squadPlayerIds = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
                ->where('gameweek_id', $currentGameweek->id)
                ->pluck('player_id')
                ->toArray();

            // Update captain status
            $this->updateCaptainStatus($fantasyTeam, $currentGameweek, $validatedData);

            // Update lineup
            $this->updateSquadLineup($fantasyTeam, $currentGameweek, $squadPlayerIds, $validatedData);
        });
    }

    /**
     * Automatically assign 4-4-2 formation for initial squad creation
     */
    protected function assignAutomatic442Formation(array $selectedPlayerIds): array
    {
        // Get player details for automatic formation assignment
        $allPlayers = Player::whereIn('id', $selectedPlayerIds)->get()->keyBy('id');

        // Group players by position
        $playersByPosition = [
            'GK' => [],
            'DEF' => [],
            'MID' => [],
            'FWD' => [],
        ];

        foreach ($selectedPlayerIds as $playerId) {
            $player = $allPlayers[$playerId];
            $normalizedPosition = $this->playerDataService->normalizePosition($player->position->value);
            $playersByPosition[$normalizedPosition][] = $playerId;
        }

        // Automatically assign starting 11 using 4-4-2 formation
        $startingPlayerIds = [];

        // 1 GK (take first available)
        if (count($playersByPosition['GK']) >= 1) {
            $startingPlayerIds[] = $playersByPosition['GK'][0];
        }

        // 4 DEF (take first 4 available)
        $startingPlayerIds = array_merge($startingPlayerIds, array_slice($playersByPosition['DEF'], 0, 4));

        // 4 MID (take first 4 available)
        $startingPlayerIds = array_merge($startingPlayerIds, array_slice($playersByPosition['MID'], 0, 4));

        // 2 FWD (take first 2 available)
        $startingPlayerIds = array_merge($startingPlayerIds, array_slice($playersByPosition['FWD'], 0, 2));

        return $startingPlayerIds;
    }

    /**
     * Create initial squad with lineup (for squad setup)
     */
    public function createInitialSquad(
        FantasyTeam $fantasyTeam,
        $currentGameweek,
        array $validatedData,
        array $playerMarketValues
    ): void {
        DB::transaction(function () use ($fantasyTeam, $currentGameweek, $validatedData, $playerMarketValues) {
            // For initial squad creation, automatically assign 4-4-2 formation
            // instead of relying on frontend lineup_players
            $startingPlayerIds = $this->assignAutomatic442Formation($validatedData['selected_players']);

            // Create fantasy players for the squad
            foreach ($validatedData['selected_players'] as $playerId) {
                $fantasyPlayer = FantasyPlayer::create([
                    'fantasy_team_id' => $fantasyTeam->id,
                    'gameweek_id' => $currentGameweek->id,
                    'player_id' => $playerId,
                    'is_captain' => $playerId == ($validatedData['captain_id'] ?? null),
                    'is_vice_captain' => $playerId == ($validatedData['vice_captain_id'] ?? null),
                    'purchase_price' => $playerMarketValues[$playerId] ?? 0,
                ]);

                // Create lineup entry based on automatic formation assignment
                $isStarting = in_array($playerId, $startingPlayerIds);
                $position = $isStarting ? 'starting' : 'bench';

                // Determine sub priority for bench players
                $subPriority = 0;
                if (!$isStarting) {
                    $player = Player::find($playerId);
                    if ($player) {
                        $subPriority = $this->playerDataService->getSubstitutePriority($player->position->value);
                    }
                }

                FantasyTeamLineup::create([
                    'fantasy_player_id' => $fantasyPlayer->id,
                    'gameweek_id' => $currentGameweek->id,
                    'position' => $position,
                    'sub_priority' => $subPriority,
                ]);
            }
        });
    }
}
