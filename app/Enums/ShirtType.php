<?php

namespace App\Enums;

enum ShirtType: string
{
    case STRIPED = 'striped';
    case FILLED = 'filled';

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get display label for the enum value
     */
    public function label(): string
    {
        return match($this) {
            self::STRIPED => 'Striped Kit',
            self::FILLED => 'Filled Kit',
        };
    }

    /**
     * Get all cases with their labels for form options
     */
    public static function options(): array
    {
        return array_map(
            fn(self $case) => ['value' => $case->value, 'label' => $case->label()],
            self::cases()
        );
    }
}
