<?php

namespace App\Enums;

enum PlayerPosition: string
{
    case GOALKEEPER = 'GK';
    case DEFENDER = 'DEF';
    case MIDFIELDER = 'MID';
    case FORWARD = 'FWD';

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get display label for the enum value
     */
    public function label(): string
    {
        return match($this) {
            self::GOALKEEPER => 'Goalkeeper',
            self::DEFENDER => 'Defender',
            self::MIDFIELDER => 'Midfielder',
            self::FORWARD => 'Forward',
        };
    }

    /**
     * Get all cases with their labels for form options
     */
    public static function options(): array
    {
        return array_map(
            fn(self $case) => ['value' => $case->value, 'label' => $case->label()],
            self::cases()
        );
    }

    /**
     * Get required count for this position in a valid squad
     */
    public function requiredCount(): int
    {
        return match($this) {
            self::GOALKEEPER => 2,
            self::DEFENDER => 5,
            self::MIDFIELDER => 5,
            self::FORWARD => 3,
        };
    }

    /**
     * Get all position requirements as an array
     */
    public static function requirements(): array
    {
        return [
            self::GOALKEEPER->value => self::GOALKEEPER->requiredCount(),
            self::DEFENDER->value => self::DEFENDER->requiredCount(),
            self::MIDFIELDER->value => self::MIDFIELDER->requiredCount(),
            self::FORWARD->value => self::FORWARD->requiredCount(),
        ];
    }
}
