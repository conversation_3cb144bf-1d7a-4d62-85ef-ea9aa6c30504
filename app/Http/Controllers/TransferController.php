<?php

namespace App\Http\Controllers;

use App\Models\Competition;
use App\Models\FantasyPlayer;
use App\Models\FantasyTeam;
use App\Models\FantasyTeamLineup;
use App\Models\Player;
use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use App\Services\TransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TransferController extends Controller
{
    protected FantasyContextService $contextService;
    protected PlayerDataService $playerDataService;
    protected SquadManagementService $squadService;
    protected TransferService $transferService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        TransferService $transferService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->transferService = $transferService;
    }
    /**
     * Show the transfers page with current squad data and available players
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (!$contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }
        
        $context = $contextResult['data'];
        $competition = $context['competition'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team for current season
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (!$fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get squad players using service
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $currentGameweek);

        // Get available players using service
        $currentSquadPlayerIds = $squadPlayers->pluck('id')->toArray();
        $availablePlayers = $this->playerDataService->getAvailablePlayersData($currentSquadPlayerIds, $currentGameweek, $competition);

        // Calculate team stats
        $squadValue = $squadPlayers->sum('price');
        $budget = $fantasyTeam->budget; // Use raw budget value from database

        // Get number of free transfers for this gameweek
        $freeTransfers = $currentGameweek->free_transfers ?? 1;

        // Get current lineup - starting players only
        $startingPlayerIds = FantasyTeamLineup::where('gameweek_id', $currentGameweek->id)
            ->where('position', 'starting')
            ->whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
                $query->where('fantasy_team_id', $fantasyTeam->id);
            })
            ->with('fantasyPlayer')
            ->get()
            ->pluck('fantasyPlayer.player_id')
            ->toArray();

        return Inertia::render('Transfers', [
            'competition' => $competition,
            'season' => $currentSeason,
            'gameweek' => $currentGameweek,
            'fantasyTeam' => $fantasyTeam,
            'availablePlayers' => $availablePlayers,
            'currentSquad' => $squadPlayers,
            'budget' => $budget,
            'freeTransfers' => $freeTransfers,
            'startingPlayerIds' => $startingPlayerIds,
        ]);
    }

    /**
     * Process player transfers
     */
    public function processTransfers(Request $request)
    {
        $user = Auth::user();
        
        // In transfer mode, we only validate squad changes, not captain/vice-captain
        $validated = $request->validate([
            'selected_players' => 'required|array|size:15',
            'selected_players.*' => 'required|exists:players,id',
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|exists:players,id',
            'transfers_made' => 'required|integer|min:0',
        ]);

        // Get competition and season information
        $tenant = \App\Models\Tenant::current();
        $currentCompetitionId = $request->session()->get('current_competition_id');

        if (!$currentCompetitionId) {
            return response()->json(['error' => 'No competition selected'], 400);
        }

        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (!$competition || !$competition->currentSeason) {
            return response()->json(['error' => 'No active season found'], 400);
        }

        $currentSeason = $competition->currentSeason;
        $currentGameweek = $currentSeason->currentSeasonPhase->currentGameweek;

        if (!$currentGameweek) {
            return response()->json(['error' => 'No active gameweek found'], 400);
        }

        // Get user's fantasy team
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (!$fantasyTeam) {
            return response()->json(['error' => 'Fantasy team not found'], 404);
        }

        // Get current squad
        $currentSquad = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->with('player')
            ->get();
        
        $currentSquadPlayerIds = $currentSquad->pluck('player_id')->toArray();
        $newSquadPlayerIds = $validated['selected_players'];
        
        // Calculate players added and removed
        $playersAdded = array_values(array_diff($newSquadPlayerIds, $currentSquadPlayerIds));
        $playersRemoved = array_values(array_diff($currentSquadPlayerIds, $newSquadPlayerIds));
        
        // Check if the number of transfers matches
        if (count($playersAdded) !== count($playersRemoved)) {
            return response()->json(['error' => 'Transfer count mismatch'], 400);
        }
        
        // Validate transfers count
        $freeTransfers = $currentGameweek->free_transfers ?? 1;
        if ($validated['transfers_made'] > count($playersAdded)) {
            return response()->json(['error' => 'Invalid transfers count'], 400);
        }
        
        // Get existing captain and vice-captain from current lineup to preserve them
        $currentLineup = FantasyTeamLineup::where('gameweek_id', $currentGameweek->id)
            ->where('position', 'starting')
            ->whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
                $query->where('fantasy_team_id', $fantasyTeam->id);
            })
            ->with('fantasyPlayer')
            ->get();

        $currentCaptainId = null;
        $currentViceCaptainId = null;
        
        foreach ($currentLineup as $lineupEntry) {
            if ($lineupEntry->fantasyPlayer->is_captain) {
                $currentCaptainId = $lineupEntry->fantasyPlayer->player_id;
            }
            if ($lineupEntry->fantasyPlayer->is_vice_captain) {
                $currentViceCaptainId = $lineupEntry->fantasyPlayer->player_id;
            }
        }

        // Add preserved captain/vice-captain to validated data
        $validated['captain_id'] = $currentCaptainId;
        $validated['vice_captain_id'] = $currentViceCaptainId;

        // Calculate squad value and transfer cost
        $transferPoints = max(0, count($playersAdded) - $freeTransfers) * 4; // 4 points per transfer above free limit

        // Validate team budget
        $newSquadValue = 0;
        $newPlayers = Player::whereIn('id', $newSquadPlayerIds)
            ->with(['marketValues' => function ($query) use ($currentGameweek) {
                $query->where('gameweek_id', $currentGameweek->id);
            }])
            ->get();

        foreach ($newPlayers as $player) {
            $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;
            $newSquadValue += $marketValue;
        }

        if ($newSquadValue > $fantasyTeam->budget) {
            return response()->json(['error' => 'Not enough budget for these transfers'], 400);
        }

        try {
            // Use transfer service to handle comprehensive transfer processing
            // This includes squad updates, transfer recording, and penalty tracking
            $this->transferService->processTransfers(
                $fantasyTeam,
                $currentGameweek,
                $validated,
                $newSquadPlayerIds,
                $playersAdded,
                $playersRemoved,
                $newSquadValue
            );

            return response()->json([
                'message' => 'Transfers processed successfully',
                'success' => true,
                'transfers_made' => count($playersAdded),
                'points_deducted' => max(0, count($playersAdded) - ($currentGameweek->free_transfers ?? 1)) * 4,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process transfers: '.$e->getMessage(),
                'success' => false,
            ], 500);
        }
    }


}
