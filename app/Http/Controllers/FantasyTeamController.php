<?php

namespace App\Http\Controllers;

use App\Enums\ShirtType;
use App\Models\Competition;
use App\Models\FantasyTeam;
use App\Models\Player;
use App\Models\Tenant;
use App\Services\FantasyContextService;
use App\Services\FantasyValidationService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class FantasyTeamController extends Controller
{
    protected FantasyContextService $contextService;
    protected PlayerDataService $playerDataService;
    protected SquadManagementService $squadService;
    protected FantasyValidationService $validationService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        FantasyValidationService $validationService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->validationService = $validationService;
    }
    /**
     * Show the form for creating a new fantasy team.
     */
    public function create()
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season
        $competition = Competition::with('currentSeason')
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        // Check if user already has a fantasy team for this season
        $existingTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        if ($existingTeam) {
            return redirect()->route('home');
        }

        return Inertia::render('FantasyTeam/Create', [
            'competition' => $competition,
            'season' => $competition->currentSeason,
            'shirtTypes' => ShirtType::options(),
        ]);
    }

    /**
     * Store a newly created fantasy team in storage.
     */
    public function store(Request $request)
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season
        $competition = Competition::with('currentSeason')
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        // Check if user already has a fantasy team for this season
        $existingTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        if ($existingTeam) {
            return redirect()->route('home');
        }

        // Validate the request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'shirt_type' => 'required|in:'.implode(',', ShirtType::values()),
            'shirt_color' => 'nullable|string|max:7',
            'strip_color' => 'nullable|string|max:7',
        ]);

        // Create the fantasy team
        $fantasyTeam = FantasyTeam::create([
            'user_id' => $user->id,
            'season_id' => $competition->currentSeason->id,
            'name' => $validated['name'],
            'shirt_type' => ShirtType::from($validated['shirt_type']),
            'shirt_color' => $validated['shirt_color'] ?? '#FF0000',
            'strip_color' => $validated['strip_color'] ?? '#FFFFFF',
            'budget' => 100,
            'total_points' => 0,
        ]);

        return redirect()->route('fantasy-team.squad')->with('success', 'Fantasy team created successfully! Now set up your squad.');
    }

    /**
     * Show the squad setup form (step 2)
     */
    public function squad()
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season and gameweek
        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        $currentSeason = $competition->currentSeason;
        $currentGameweek = $currentSeason->currentSeasonPhase?->currentGameweek ?? null;

        if (! $currentGameweek) {
            return redirect()->route('home')->with('error', 'No active gameweek found.');
        }

        // Get user's fantasy team
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create');
        }

        // Get available players for the current season
        // This would typically be players from teams participating in the competition
        $availablePlayers = \App\Models\Player::whereHas('teams', function ($query) use ($currentSeason) {
            $query->whereHas('seasons', function ($seasonQuery) use ($currentSeason) {
                $seasonQuery->where('seasons.id', $currentSeason->id);
            });
        })->with('teams')->get();

        // Get current squad if any
        $currentSquad = \App\Models\FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->with(['player', 'fantasyTeamLineups'])
            ->get();

        // Check if user already has a valid squad - if so, redirect to home
        if ($this->hasValidSquad($fantasyTeam, $currentGameweek)) {
            return redirect()->route('home')->with('success', 'Your squad is already set up for this gameweek.');
        }

        return Inertia::render('FantasyTeam/Squad', [
            'competition' => $competition,
            'season' => $currentSeason,
            'gameweek' => $currentGameweek,
            'fantasyTeam' => $fantasyTeam,
            'availablePlayers' => $availablePlayers,
            'currentSquad' => $currentSquad,
            'budget' => $fantasyTeam->budget, // Use raw budget value from database
        ]);
    }

    /**
     * Store the squad setup (step 2)
     */
    public function storeSquad(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('home');
        }

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (!$contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }
        
        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create');
        }

        // Validate the squad selection
        $validated = $request->validate([
            'selected_players' => 'required|array|size:15',
            'selected_players.*' => 'required|integer|exists:players,id',
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|integer|exists:players,id',
            'captain_id' => 'required|integer|exists:players,id',
            'vice_captain_id' => 'required|integer|exists:players,id',
        ]);

        // Validate squad composition using service
        $compositionValidation = $this->validationService->validateSquadComposition($validated['selected_players']);
        if (!$compositionValidation['valid']) {
            return redirect()->back()->withErrors([
                'squad' => implode(' ', $compositionValidation['errors'])
            ]);
        }

        // Skip lineup and captain validation for initial squad creation
        // The service will automatically assign 4-4-2 formation and handle captain validation
        // This is different from transfers where user selects their own formation

        // Calculate player market values and validate budget
        $allPlayers = Player::whereIn('id', $validated['selected_players'])->get()->keyBy('id');
        $playerMarketValues = [];
        
        foreach ($validated['selected_players'] as $playerId) {
            $player = $allPlayers[$playerId];
            $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 5; // Default 5 if no market value
            $playerMarketValues[$playerId] = $marketValue;
        }

        // Validate budget using service
        $budgetValidation = $this->validationService->validateBudget(
            $validated['selected_players'],
            $playerMarketValues,
            $fantasyTeam->budget
        );
        
        if (!$budgetValidation['valid']) {
            return redirect()->back()->withErrors([
                'budget' => 'Squad cost ('.number_format($budgetValidation['totalCost'], 1).') exceeds your budget ('.number_format($budgetValidation['availableBudget'], 1).')',
            ]);
        }

        // Clear existing squad for this gameweek
        \App\Models\FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->delete();

        // Update fantasy team budget after squad creation
        $remainingBudget = $fantasyTeam->budget - $budgetValidation['totalCost'];
        $fantasyTeam->updateBudget($remainingBudget);

        // Use squad management service to create initial squad with lineup
        $this->squadService->createInitialSquad(
            $fantasyTeam,
            $currentGameweek,
            $validated,
            $playerMarketValues
        );

        return redirect()->route('home')->with('success', 'Squad setup completed successfully!');
    }

    /**
     * Check if the fantasy team has a valid squad for the given gameweek
     */
    private function hasValidSquad(FantasyTeam $fantasyTeam, $gameweek)
    {
        return $this->validationService->hasValidSquad($fantasyTeam, $gameweek);
    }
}
