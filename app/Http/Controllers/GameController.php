<?php

namespace App\Http\Controllers;

use App\Models\Competition;
use App\Models\FantasyPlayer;
use App\Models\FantasyTeam;
use App\Models\FantasyTeamLineup;
use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class GameController extends Controller
{
    protected FantasyContextService $contextService;

    protected PlayerDataService $playerDataService;

    protected SquadManagementService $squadService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
    }

    /**
     * Show the my team page with current squad data
     */
    public function myTeam(Request $request)
    {
        $user = Auth::user();

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team for current season
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get squad players using service
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $currentGameweek);

        // Get current lineup - starting players only
        $startingPlayerIds = FantasyTeamLineup::where('gameweek_id', $currentGameweek->id)
            ->where('position', 'starting')
            ->whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
                $query->where('fantasy_team_id', $fantasyTeam->id);
            })
            ->with('fantasyPlayer')
            ->get()
            ->pluck('fantasyPlayer.player_id')
            ->toArray();

        // Calculate team stats
        $squadValue = $squadPlayers->sum('price');
        $budget = $fantasyTeam->budget; // Use raw budget value from database
        $balance = $budget; // Budget represents the current available budget

        return Inertia::render('Game', [
            'teamData' => [
                'budget' => $budget,
                'squadValue' => $squadValue,
                'balance' => $balance,
                'freeTransfers' => $currentGameweek->transfers_allowed ?? 1,
            ],
            'squadPlayers' => $squadPlayers,
            'startingPlayerIds' => $startingPlayerIds,
            'fantasyTeam' => [
                'id' => $fantasyTeam->id,
                'name' => $fantasyTeam->name,
                'kit_type' => $fantasyTeam->kit_type,
                'kit_primary_color' => $fantasyTeam->kit_primary_color,
                'kit_secondary_color' => $fantasyTeam->kit_secondary_color,
            ],
            'currentGameweek' => [
                'id' => $currentGameweek->id,
                'name' => $currentGameweek->name,
                'deadline' => $currentGameweek->deadline,
            ],
        ]);
    }

    /**
     * Update team lineup and captain selections
     */
    public function updateTeam(Request $request)
    {
        $validated = $request->validate([
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|integer|exists:players,id',
            'captain_id' => 'required|integer|exists:players,id',
            'vice_captain_id' => 'required|integer|exists:players,id',
            'formation' => 'required|string|in:4-4-2,4-3-3,3-5-2,5-3-2,4-5-1,5-4-1,3-4-3',
        ]);

        $user = Auth::user();
        $tenant = \App\Models\Tenant::current();

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');

        if (! $currentCompetitionId) {
            return response()->json(['error' => 'No competition selected'], 400);
        }

        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition) {
            return response()->json(['error' => 'Competition not found'], 404);
        }

        $currentSeason = $competition->currentSeason;
        $currentGameweek = $currentSeason->currentSeasonPhase->currentGameweek ?? null;

        if (! $currentSeason || ! $currentGameweek) {
            return response()->json(['error' => 'No active gameweek found'], 400);
        }

        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return response()->json(['error' => 'Fantasy team not found'], 404);
        }

        // Validate that all players belong to the user's squad
        $squadPlayerIds = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->pluck('player_id')
            ->toArray();

        $invalidPlayers = array_diff($validated['lineup_players'], $squadPlayerIds);
        if (! empty($invalidPlayers)) {
            return response()->json(['error' => 'Some players are not in your squad'], 400);
        }

        if (! in_array($validated['captain_id'], $validated['lineup_players'])) {
            return response()->json(['error' => 'Captain must be in starting lineup'], 400);
        }

        if (! in_array($validated['vice_captain_id'], $validated['lineup_players'])) {
            return response()->json(['error' => 'Vice captain must be in starting lineup'], 400);
        }

        try {
            // Use squad management service to handle lineup updates
            $this->squadService->updateLineupOnly($fantasyTeam, $currentGameweek, $validated);

            return response()->json([
                'message' => 'Team updated successfully',
                'success' => true,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update team: '.$e->getMessage(),
                'success' => false,
            ], 500);
        }
    }
}
