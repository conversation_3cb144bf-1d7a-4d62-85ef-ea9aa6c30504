<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'auth' => [
                'user' => fn () => Auth::user() ? [
                    'id' => Auth::user()->id,
                    'name' => Auth::user()->name,
                    'first_name' => Auth::user()->first_name,
                    'last_name' => Auth::user()->last_name,
                    'email' => Auth::user()->email,
                    'avatar' => Auth::user()->avatar,
                ] : null,
            ],
            'tenant' => function () {
                $tenant = Tenant::current();

                return $tenant ? [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'domain' => $tenant->domain,
                    'database' => $tenant->database,
                    'created_at' => $tenant->created_at,
                    'updated_at' => $tenant->updated_at,
                ] : null;
            },
            'competitions' => function () {
                $tenant = Tenant::current();
                
                return $tenant ? $tenant->competitions()->select('id', 'name', 'slug')->get() : [];
            },
            'currentCompetition' => function () use ($request) {
                $competitionId = $request->session()->get('current_competition_id');
                
                if ($competitionId) {
                    $tenant = Tenant::current();
                    return $tenant ? $tenant->competitions()->where('id', $competitionId)->select('id', 'name', 'slug')->first() : null;
                }
                
                return null;
            },
            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'error' => fn () => $request->session()->get('error'),
                'success' => fn () => $request->session()->get('success'),
            ],
            'ziggy' => fn () => [
                // ziggy data
            ],
        ];
    }
}
