<?php

namespace App\Http\Middleware;

use App\Enums\PlayerPosition;
use App\Models\Competition;
use App\Models\FantasyPlayer;
use App\Models\FantasyTeam;
use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureValidFantasySquad
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for fantasy team creation/squad setup routes to prevent redirect loop
        if ($request->routeIs('fantasy-team.create') ||
            $request->routeIs('fantasy-team.store') ||
            $request->routeIs('fantasy-team.squad') ||
            $request->routeIs('fantasy-team.squad.store') ||
            $request->routeIs('competition.set-current')) {
            return $next($request);
        }

        $tenant = Tenant::current();
        $user = Auth::user();

        // If no tenant or user, let other middleware handle this
        if (! $tenant || ! $user) {
            return $next($request);
        }

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');

        if (! $currentCompetitionId) {
            return $next($request);
        }

        // Get the current competition with its current season
        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return $next($request);
        }

        $currentSeason = $competition->currentSeason;
        $currentGameweek = $currentSeason->currentSeasonPhase?->currentGameweek ?? null;

        if (! $currentGameweek) {
            return $next($request);
        }

        // Get user's fantasy team for the current season
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            // This should be handled by EnsureFantasyTeamExists middleware
            return $next($request);
        }

        // Check if the fantasy team has a valid squad for the current gameweek
        $squadPlayers = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->with(['player', 'fantasyTeamLineups' => function ($query) use ($currentGameweek) {
                $query->where('gameweek_id', $currentGameweek->id);
            }])
            ->get();

        // Validate squad composition
        if (! $this->isValidSquad($squadPlayers)) {
            return redirect()->route('fantasy-team.squad');
        }

        return $next($request);
    }

    /**
     * Validate if the squad has the correct composition
     * Required: 15 players total (2 GK, 5 DEF, 5 MID, 3 FWD) with 11 starting and 4 on bench
     */
    private function isValidSquad($squadPlayers): bool
    {
        if ($squadPlayers->count() !== 15) {
            return false;
        }

        // Count players by position using enum values
        $positionCounts = array_fill_keys(PlayerPosition::values(), 0);
        $startingCount = 0;
        $benchCount = 0;
        $unassignedCount = 0;

        foreach ($squadPlayers as $fantasyPlayer) {
            $position = $fantasyPlayer->player->position;

            // Position is already cast to PlayerPosition enum
            if ($position instanceof PlayerPosition) {
                $positionCounts[$position->value]++;
            }

            // Check player's lineup status
            $lineupEntries = $fantasyPlayer->fantasyTeamLineups;

            if ($lineupEntries->isEmpty()) {
                // No lineup entry - player is unassigned
                $unassignedCount++;
            } else {
                // Check if player is starting or on bench based on position field
                $lineupEntry = $lineupEntries->first();
                if ($lineupEntry && $lineupEntry->position === 'starting') {
                    $startingCount++;
                } elseif ($lineupEntry && $lineupEntry->position === 'bench') {
                    $benchCount++;
                } else {
                    // Unknown position, treat as unassigned
                    $unassignedCount++;
                }
            }
        }

        // Validate position requirements using enum requirements
        $requirements = PlayerPosition::requirements();
        foreach ($requirements as $position => $requiredCount) {
            if ($positionCounts[$position] !== $requiredCount) {
                return false;
            }
        }

        // Validate lineup composition: exactly 11 starting, 4 on bench, 0 unassigned
        return $startingCount === 11 && $benchCount === 4 && $unassignedCount === 0;
    }
}
