<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureCompetitionExists
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = Tenant::current();
        
        // If no tenant, let other middleware handle this
        if (!$tenant) {
            return $next($request);
        }
        
        // Get all competitions for the current tenant
        $competitions = $tenant->competitions()->select('id', 'name', 'slug')->get();
        
        // Check if tenant has at least one competition
        if ($competitions->isEmpty()) {
            abort(403, 'No competition set for this tenant. Please contact your administrator.');
        }
        
        // Check if current competition is set in session
        $currentCompetitionId = $request->session()->get('current_competition_id');
        
        // If no current competition is set, or the set competition doesn't exist for this tenant
        if (!$currentCompetitionId || !$competitions->contains('id', $currentCompetitionId)) {
            // Set the first competition as current
            $firstCompetition = $competitions->first();
            $request->session()->put('current_competition_id', $firstCompetition->id);
        }
        
        return $next($request);
    }
}
