<?php

namespace App\Http\Middleware;

use App\Models\Competition;
use App\Models\FantasyTeam;
use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureFantasyTeamExists
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for fantasy team creation routes and competition switching to prevent redirect loop
        if (
            $request->routeIs('fantasy-team.squad') ||
            $request->routeIs('fantasy-team.squad.store') ||
            $request->routeIs('fantasy-team.create') ||
            $request->routeIs('fantasy-team.store') ||
            $request->routeIs('competition.set-current')) {
            return $next($request);
        }

        $tenant = Tenant::current();
        $user = Auth::user();

        // If no tenant or user, let other middleware handle this
        if (! $tenant || ! $user) {
            return $next($request);
        }

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');

        if (! $currentCompetitionId) {
            return $next($request);
        }

        // Get the current competition with its current season
        $competition = Competition::with('currentSeason')
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return $next($request);
        }

        // Check if user has a fantasy team for the current season
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        // If no fantasy team exists, redirect to team creation page
        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create');
        }

        return $next($request);
    }
}
