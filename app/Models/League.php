<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class League extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'name',
        'season_id',
        'owner_id',
        'type',
        'invite_code',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($league) {
            if (empty($league->tenant_id) && Tenant::current()) {
                $league->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function season()
    {
        return $this->belongsTo(Season::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function fantasyTeams()
    {
        return $this->belongsToMany(FantasyTeam::class);
    }

    public function rankingLeagues()
    {
        return $this->hasMany(RankingLeague::class);
    }
}
