<?php

namespace App\Models;

use App\Enums\PlayerPosition;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Player extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'birthday',
        'country',
        'position',
        'image',
    ];

    protected $casts = [
        'position' => PlayerPosition::class,
    ];

    public function teams()
    {
        return $this->belongsToMany(Team::class);
    }

    public function fantasyPlayers()
    {
        return $this->hasMany(FantasyPlayer::class);
    }

    public function inFantasyTransfers()
    {
        return $this->hasMany(FantasyTransfer::class, 'player_in_id');
    }

    public function outFantasyTransfers()
    {
        return $this->hasMany(FantasyTransfer::class, 'player_out_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function fantasyPoints()
    {
        return $this->hasMany(FantasyPoint::class);
    }

    public function marketValues()
    {
        return $this->hasMany(PlayerMarketValue::class);
    }

    /**
     * Get the current market value for the player (latest gameweek)
     */
    public function getCurrentMarketValue(): ?int
    {
        return $this->marketValues()
            ->join('gameweeks', 'player_market_values.gameweek_id', '=', 'gameweeks.id')
            ->orderBy('gameweeks.deadline', 'desc')
            ->value('market_value');
    }

    /**
     * Get market value for a specific gameweek
     */
    public function getMarketValueForGameweek(int $gameweekId): ?int
    {
        return $this->marketValues()
            ->where('gameweek_id', $gameweekId)
            ->value('market_value');
    }

    /**
     * Accessor for market_value to maintain backward compatibility
     */
    public function getMarketValueAttribute(): ?int
    {
        return $this->getCurrentMarketValue();
    }
}
