<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gameweek extends Model
{
    use HasFactory;

    protected $fillable = [
        'season_phase_id',
        'name',
        'start_date',
        'end_date',
        'rules',
        'status',
    ];

    protected $casts = [
        'rules' => 'array',
    ];

    public function seasonPhase()
    {
        return $this->belongsTo(SeasonPhase::class);
    }

    public function fantasyPlayers()
    {
        return $this->hasMany(FantasyPlayer::class);
    }

    public function games()
    {
        return $this->hasMany(Game::class);
    }

    public function rankingGlobals()
    {
        return $this->hasMany(RankingGlobal::class);
    }

    public function rankingFavoriteTeams()
    {
        return $this->hasMany(RankingFavoriteTeam::class);
    }

    public function rankingJoinedGameweeks()
    {
        return $this->hasMany(RankingJoinedGameweek::class, 'join_gameweek_id');
    }

    public function rankingGameweeks()
    {
        return $this->hasMany(RankingJoinedGameweek::class, 'gameweek_id');
    }

    public function rankingLeagues()
    {
        return $this->hasMany(RankingLeague::class);
    }
}
