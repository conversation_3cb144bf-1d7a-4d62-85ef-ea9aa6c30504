<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class FantasyTeamGameweek extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $fillable = [
        'fantasy_team_id',
        'gameweek_id',
        'transfer_cost_penalty',
        'total_points',
        'transfers_made',
        'free_transfers_used',
        'points_deducted',
    ];

    protected $casts = [
        'transfer_cost_penalty' => 'decimal:2',
        'total_points' => 'integer',
        'transfers_made' => 'integer',
        'free_transfers_used' => 'integer',
        'points_deducted' => 'integer',
    ];

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function transfers()
    {
        return $this->hasMany(FantasyTransfer::class, 'fantasy_team_id', 'fantasy_team_id')
            ->where('gameweek_id', $this->gameweek_id);
    }
}
