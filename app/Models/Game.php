<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $fillable = [
        'gameweek_id',
        'home_team_id',
        'away_team_id',
        'game_date',
        'home_score',
        'away_score',
        'status',
    ];

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function homeTeam()
    {
        return $this->belongsTo(Team::class, 'home_team_id');
    }

    public function awayTeam()
    {
        return $this->belongsTo(Team::class, 'away_team_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function fantasyPoints()
    {
        return $this->hasMany(FantasyPoint::class);
    }
}
