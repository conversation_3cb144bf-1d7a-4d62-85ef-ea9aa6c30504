<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'short_name',
        'code_name',
        'logo',
        'shirt',
        'gk_shirt',
        'president',
        'founded_at',
    ];

    public function seasons()
    {
        return $this->belongsToMany(Season::class);
    }

    public function players()
    {
        return $this->belongsToMany(Player::class);
    }

    public function homeGames()
    {
        return $this->hasMany(Game::class, 'home_team_id');
    }

    public function awayGames()
    {
        return $this->hasMany(Game::class, 'away_team_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function rankingFavoriteTeams()
    {
        return $this->hasMany(RankingFavoriteTeam::class);
    }
}
