<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class OtpCode extends Model
{
    protected $fillable = [
        'phone',
        'code',
        'expires_at',
        'used'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used' => 'boolean',
    ];

    /**
     * Generate a new OTP code for a phone number
     */
    public static function generateForPhone(string $phone): self
    {
        // Delete any existing unused codes for this phone
        self::where('phone', $phone)->where('used', false)->delete();

        // Generate new 6-digit code
        $code = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

        return self::create([
            'phone' => $phone,
            'code' => $code,
            'expires_at' => Carbon::now()->addMinutes(10), // 10 minutes expiry
            'used' => false,
        ]);
    }

    /**
     * Verify an OTP code
     */
    public static function verify(string $phone, string $code): bool
    {
        $otpCode = self::where('phone', $phone)
            ->where('code', $code)
            ->where('used', false)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($otpCode) {
            $otpCode->update(['used' => true]);
            return true;
        }

        return false;
    }

    /**
     * Check if code is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }
}
