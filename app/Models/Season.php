<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Season extends Model
{
    use HasFactory;

    protected $fillable = [
        'competition_id',
        'name',
        'start_date',
        'end_date',
    ];

    public function competition()
    {
        return $this->belongsTo(Competition::class);
    }

    public function competitions()
    {
        return $this->hasMany(Competition::class, 'current_season_id');
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class);
    }

    public function seasonPhases()
    {
        return $this->hasMany(SeasonPhase::class);
    }

    public function fantasyTeams()
    {
        return $this->hasMany(FantasyTeam::class);
    }

    public function leagues()
    {
        return $this->hasMany(League::class);
    }

    /**
     * Get the current active season phase
     */
    public function currentSeasonPhase()
    {
        return $this->hasOne(SeasonPhase::class)
            ->where('status', 'ongoing')
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get the current gameweek from the current season phase
     */
    public function currentGameweek()
    {
        return $this->hasManyThrough(Gameweek::class, SeasonPhase::class)
            ->where('season_phases.status', 'ongoing')
            ->where('gameweeks.status', 'ongoing')
            ->orderBy('gameweeks.start_date', 'desc')
            ->limit(1);
    }
}
