<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\RankingLeague;
use Illuminate\Database\Eloquent\Factories\Factory;

class RankingLeagueFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RankingLeague::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'rank_gameweek' => fake()->numberBetween(1, 10000),
            'rank' => fake()->unique()->numberBetween(1, 10000),
            'points' => fake()->numberBetween(0, 3000),
            'league_id' => \App\Models\League::factory(),
            'gameweek_id' => \App\Models\Gameweek::factory(),
            'fantasy_team_id' => \App\Models\FantasyTeam::factory(),
        ];
    }
}
