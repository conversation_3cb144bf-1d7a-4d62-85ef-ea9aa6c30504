<?php

namespace Database\Factories;

use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

class TenantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Tenant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'domain' => parse_url(fake()->url(), PHP_URL_HOST),
            'database' => fake()->randomNumber(5),
            'logo' => fake()->word(),
            'is_active' => fake()->boolean(),
            'background_image' => fake()->word(),
            'primary_color' => fake()->word(),
            'secondary_color' => fake()->word(),
        ];
    }
}
