<?php

namespace Database\Factories;

use App\Models\Competition;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompetitionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Competition::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $competitions = [
            'Premier League',
            'Championship',
            'League One',
            'League Two',
            'FA Cup',
            'EFL Cup',
            'Community Shield',
            'UEFA Champions League',
            'UEFA Europa League',
            'UEFA Conference League'
        ];
        
        $competitionName = fake()->randomElement($competitions);
        $slug = strtolower(str_replace(' ', '-', $competitionName));
        
        // Determine competition type based on name
        $competitionType = 'league';
        if (str_contains($competitionName, 'Cup') || str_contains($competitionName, 'Shield')) {
            $competitionType = 'cup';
        } elseif (str_contains($competitionName, 'UEFA') || str_contains($competitionName, 'Champions')) {
            $competitionType = 'mixed';
        }
        
        return [
            'name' => $competitionName,
            'logo' => "/images/competitions/{$slug}.png",
            'slug' => $slug,
            'type' => $competitionType,
            'status' => fake()->randomElement(['upcoming', 'ongoing', 'completed', 'archived']),
            'current_season_id' => null
        ];
    }
    
    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (\App\Models\Competition $competition) {
            if ($competition->current_season_id === null) {
                $season = \App\Models\Season::factory()->create([
                    'competition_id' => $competition->id,
                ]);
                $competition->current_season_id = $season->id;
                $competition->save();
            }
        });
    }
}
