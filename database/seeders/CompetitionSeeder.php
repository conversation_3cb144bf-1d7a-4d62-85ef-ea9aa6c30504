<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class CompetitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the Coaching Foot tenant
        $coachingFootTenant = Tenant::where('domain', 'cf.test')->first();

        if ($coachingFootTenant) {
            // Create specific competitions for Coaching Foot tenant
            // Note: We create these directly without using the factory to prevent
            // the factory's configure() method from automatically creating seasons
            $ligue1Tunisie = Competition::create([
                'name' => 'Ligue 1 Tunisie',
                'slug' => 'ligue-1-tunisie',
                'logo' => '/images/competitions/ligue-1-tunisie.png',
                'type' => 'league',
                'status' => 'ongoing',
                'current_season_id' => null,
            ]);

            $premierLeague = Competition::create([
                'name' => 'Premier League',
                'slug' => 'premier-league',
                'logo' => '/images/competitions/premier-league.png',
                'type' => 'league',
                'status' => 'ongoing',
                'current_season_id' => null,
            ]);

            // Attach competitions to the Coaching Foot tenant using pivot table
            $coachingFootTenant->competitions()->attach([$ligue1Tunisie->id, $premierLeague->id]);
        }

        // Create additional random competitions
        Competition::factory()
            ->count(3)
            ->create();
    }
}
