<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Season;
use App\Models\Team;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the specific competitions and their current seasons
        $ligue1Tunisie = Competition::where('slug', 'ligue-1-tunisie')->first();
        $premierLeague = Competition::where('slug', 'premier-league')->first();

        // Create 16 Tunisian teams for Ligue 1 Tunisie
        if ($ligue1Tunisie && $ligue1Tunisie->currentSeason) {
            $tunisianTeams = [
                ['name' => 'Espérance Sportive de Tunis', 'short_name' => 'EST', 'code_name' => 'EST'],
                ['name' => 'Club Africain', 'short_name' => 'CA', 'code_name' => 'CAF'],
                ['name' => 'Étoile Sportive du Sahel', 'short_name' => 'ESS', 'code_name' => 'ESS'],
                ['name' => 'Club Sportif Sfaxien', 'short_name' => 'CSS', 'code_name' => 'CSS'],
                ['name' => 'Stade Tunisien', 'short_name' => 'ST', 'code_name' => 'STU'],
                ['name' => 'Union Sportive Monastirienne', 'short_name' => 'USM', 'code_name' => 'USM'],
                ['name' => 'Club Athlétique Bizertin', 'short_name' => 'CAB', 'code_name' => 'CAB'],
                ['name' => 'Olympique Béja', 'short_name' => 'OB', 'code_name' => 'OBE'],
                ['name' => 'Association Sportive Soliman', 'short_name' => 'ASS', 'code_name' => 'ASS'],
                ['name' => 'Jeunesse Sportive Kairouanaise', 'short_name' => 'JSK', 'code_name' => 'JSK'],
                ['name' => 'Union Sportive Tataouine', 'short_name' => 'UST', 'code_name' => 'UST'],
                ['name' => 'Étoile Sportive de Métlaoui', 'short_name' => 'ESM', 'code_name' => 'ESM'],
                ['name' => 'Club Sportif Hammam-Lif', 'short_name' => 'CSHL', 'code_name' => 'CSH'],
                ['name' => 'Avenir Sportif de La Marsa', 'short_name' => 'ASM', 'code_name' => 'ASM'],
                ['name' => 'Croissant Sportif de M\'saken', 'short_name' => 'CSM', 'code_name' => 'CSM'],
                ['name' => 'Olympique de Médenine', 'short_name' => 'OM', 'code_name' => 'OME'],
            ];

            foreach ($tunisianTeams as $teamData) {
                $team = Team::factory()->create([
                    'name' => $teamData['name'],
                    'short_name' => $teamData['short_name'],
                    'code_name' => $teamData['code_name'],
                    'logo' => "/images/teams/logos/{$teamData['code_name']}.png",
                    'shirt' => "/images/teams/shirts/{$teamData['code_name']}_home.png",
                    'gk_shirt' => "/images/teams/shirts/{$teamData['code_name']}_gk.png",
                ]);

                // Attach team to the Ligue 1 Tunisie season

                $ligue1Tunisie->currentSeason->teams()->attach($team->id);
            }
        }

        // Create 20 Premier League teams
        if ($premierLeague && $premierLeague->currentSeason) {
            $premierLeagueTeams = [
                ['name' => 'Arsenal', 'short_name' => 'ARS', 'code_name' => 'ARS'],
                ['name' => 'Chelsea', 'short_name' => 'CHE', 'code_name' => 'CHE'],
                ['name' => 'Liverpool', 'short_name' => 'LIV', 'code_name' => 'LIV'],
                ['name' => 'Manchester City', 'short_name' => 'MCI', 'code_name' => 'MCI'],
                ['name' => 'Manchester United', 'short_name' => 'MUN', 'code_name' => 'MUN'],
                ['name' => 'Tottenham Hotspur', 'short_name' => 'TOT', 'code_name' => 'TOT'],
                ['name' => 'Newcastle United', 'short_name' => 'NEW', 'code_name' => 'NEW'],
                ['name' => 'Brighton & Hove Albion', 'short_name' => 'BHA', 'code_name' => 'BHA'],
                ['name' => 'Aston Villa', 'short_name' => 'AVL', 'code_name' => 'AVL'],
                ['name' => 'West Ham United', 'short_name' => 'WHU', 'code_name' => 'WHU'],
                ['name' => 'Crystal Palace', 'short_name' => 'CRY', 'code_name' => 'CRY'],
                ['name' => 'Fulham', 'short_name' => 'FUL', 'code_name' => 'FUL'],
                ['name' => 'Wolverhampton Wanderers', 'short_name' => 'WOL', 'code_name' => 'WOL'],
                ['name' => 'Everton', 'short_name' => 'EVE', 'code_name' => 'EVE'],
                ['name' => 'Brentford', 'short_name' => 'BRE', 'code_name' => 'BRE'],
                ['name' => 'Nottingham Forest', 'short_name' => 'NFO', 'code_name' => 'NFO'],
                ['name' => 'AFC Bournemouth', 'short_name' => 'BOU', 'code_name' => 'BOU'],
                ['name' => 'Sheffield United', 'short_name' => 'SHU', 'code_name' => 'SHU'],
                ['name' => 'Burnley', 'short_name' => 'BUR', 'code_name' => 'BUR'],
                ['name' => 'Luton Town', 'short_name' => 'LUT', 'code_name' => 'LUT'],
            ];

            foreach ($premierLeagueTeams as $teamData) {
                $team = Team::factory()->create([
                    'name' => $teamData['name'],
                    'short_name' => $teamData['short_name'],
                    'code_name' => $teamData['code_name'],
                    'logo' => "/images/teams/logos/{$teamData['code_name']}.png",
                    'shirt' => "/images/teams/shirts/{$teamData['code_name']}_home.png",
                    'gk_shirt' => "/images/teams/shirts/{$teamData['code_name']}_gk.png",
                ]);

                // Attach team to the Premier League season
                $premierLeague->currentSeason->teams()->attach($team->id);
            }
        }

        // Create additional random teams
        Team::factory()
            ->count(3)
            ->create();
    }
}
