import React from 'react';

interface FootballShirtPreviewProps {
    shirtType: 'striped' | 'filled';
    primaryColor: string;
    secondaryColor: string;
    size?: 'small' | 'medium' | 'large';
    className?: string;
}

const FootballShirtPreview: React.FC<FootballShirtPreviewProps> = ({
    shirtType,
    primaryColor,
    secondaryColor,
    size = 'medium',
    className = ''
}) => {
    // Size configurations
    const sizeConfig = {
        small: {
            width: '80px',
            height: '95px',
            neckWidth: '30px',
            neckHeight: '17px',
            neckTop: '-7px',
            neckBorder: '3px',
            sleeveWidth: '27px',
            sleeveHeight: '30px',
            sleeveOffset: '-18px',
            stripeWidth: '17px'
        },
        medium: {
            width: '120px',
            height: '140px',
            neckWidth: '45px',
            neckHeight: '25px',
            neckTop: '-10px',
            neckBorder: '4px',
            sleeveWidth: '40px',
            sleeveHeight: '45px',
            sleeveOffset: '-27px',
            stripeWidth: '25px'
        },
        large: {
            width: '160px',
            height: '185px',
            neckWidth: '60px',
            neckHeight: '33px',
            neckTop: '-13px',
            neckBorder: '5px',
            sleeveWidth: '53px',
            sleeveHeight: '60px',
            sleeveOffset: '-36px',
            stripeWidth: '33px'
        }
    };

    const config = sizeConfig[size];

    return (
        <div className={`relative ${className}`}>
            <div 
                className="relative overflow-hidden"
                style={{
                    width: config.width,
                    height: config.height,
                    borderRadius: '6px 6px 4px 4px',
                    boxShadow: '0 5px 10px rgba(0,0,0,0.1), 0 3px 3px rgba(0,0,0,0.15)',
                    background: shirtType === 'striped' 
                        ? `repeating-linear-gradient(to right, ${primaryColor}, ${primaryColor} ${config.stripeWidth}, ${secondaryColor} ${config.stripeWidth}, ${secondaryColor} ${parseInt(config.stripeWidth) * 2}px)`
                        : primaryColor
                }}
            >
                {/* Neck hole */}
                <div 
                    className="absolute"
                    style={{
                        top: config.neckTop,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        width: config.neckWidth,
                        height: config.neckHeight,
                        background: 'rgb(248 250 252)',
                        borderRadius: '50%',
                        zIndex: 2,
                        border: `${config.neckBorder} solid ${shirtType === 'striped' ? secondaryColor : primaryColor}`
                    }}
                />
                
                {/* Left sleeve */}
                <div 
                    className="absolute"
                    style={{
                        top: '0',
                        left: config.sleeveOffset,
                        width: config.sleeveWidth,
                        height: config.sleeveHeight,
                        transform: 'rotate(40deg)',
                        zIndex: -1,
                        backgroundColor: shirtType === 'striped' ? primaryColor : secondaryColor
                    }}
                />
                
                {/* Right sleeve */}
                <div 
                    className="absolute"
                    style={{
                        top: '0',
                        right: config.sleeveOffset,
                        width: config.sleeveWidth,
                        height: config.sleeveHeight,
                        transform: 'rotate(-40deg)',
                        zIndex: -1,
                        backgroundColor: shirtType === 'striped' ? primaryColor : secondaryColor
                    }}
                />
            </div>
        </div>
    );
};

export default FootballShirtPreview;
