import React from "react";
import { Badge } from "@/Components/ui/badge";

interface LeaderboardRowProps {
    rank: number;
    name: string;
    teamName: string;
    points: number;
    lastWeek: number;
    isCurrentUser?: boolean;
}

export default function LeaderboardRow({
    rank,
    name,
    teamName,
    points,
    lastWeek,
    isCurrentUser = false
}: LeaderboardRowProps) {
    const getRankColor = (position: number) => {
        if (position === 1) return "bg-yellow-100 text-yellow-800";
        if (position === 2) return "bg-gray-100 text-gray-800";
        if (position === 3) return "bg-orange-100 text-orange-800";
        return "bg-slate-100 text-slate-800";
    };

    return (
        <div className={`flex items-center justify-between p-4 rounded-lg border ${
            isCurrentUser ? 'bg-blue-50 border-blue-200' : 'bg-white border-slate-200'
        }`}>
            <div className="flex items-center gap-4">
                <Badge className={`w-8 h-8 rounded-full flex items-center justify-center ${getRankColor(rank)}`}>
                    {rank}
                </Badge>
                <div>
                    <p className={`font-medium ${isCurrentUser ? 'text-blue-900' : 'text-slate-900'}`}>
                        {name}
                    </p>
                    <p className={`text-sm ${isCurrentUser ? 'text-blue-600' : 'text-slate-600'}`}>
                        {teamName}
                    </p>
                </div>
            </div>
            <div className="text-right">
                <p className={`font-bold ${isCurrentUser ? 'text-blue-900' : 'text-slate-900'}`}>
                    {points.toLocaleString()}
                </p>
                <p className={`text-sm ${isCurrentUser ? 'text-blue-600' : 'text-slate-600'}`}>
                    GW: {lastWeek}
                </p>
            </div>
        </div>
    );
}
