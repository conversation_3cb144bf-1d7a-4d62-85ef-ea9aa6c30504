import React, { useState, FC, useEffect, useRef, useMemo } from "react";
import StatsCard from "./StatsCard";

// --- TYPE DEFINITIONS ---
interface Player {
    id: number | string; // Can be number for real players, string for placeholders
    name: string;
    position: "G<PERSON>" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

// --- CONSTANTS ---

const MAX_PLAYERS_PER_TEAM = 3;
const POINTS_PER_TRANSFER = 4;

const squadLayout = {
    GK: [
        { x: 40, y: 88 },
        { x: 60, y: 88 },
    ],
    DEF: [
        { x: 15, y: 68 },
        { x: 32.5, y: 68 },
        { x: 50, y: 68 },
        { x: 67.5, y: 68 },
        { x: 85, y: 68 },
    ],
    MID: [
        { x: 15, y: 48 },
        { x: 32.5, y: 48 },
        { x: 50, y: 48 },
        { x: 67.5, y: 48 },
        { x: 85, y: 48 },
    ],
    FWD: [
        { x: 30, y: 28 },
        { x: 50, y: 28 },
        { x: 70, y: 28 },
    ],
};

const createPlaceholderSquad = (): Player[] => {
    const placeholders: Player[] = [];
    let idCounter = 0;
    (Object.keys(squadLayout) as Array<keyof typeof squadLayout>).forEach(
        (pos) => {
            squadLayout[pos].forEach(() => {
                placeholders.push({
                    id: `placeholder-${idCounter++}`,
                    name: `Add ${pos}`,
                    position: pos,
                    team: "",
                    price: 0,
                });
            });
        }
    );
    return placeholders;
};

// --- UTILITY & VALIDATION ---
const calculateSquadValue = (squad: Player[]): number =>
    squad.reduce((total, player) => total + player.price, 0);

const validateTransfer = (
    playerOut: Player,
    playerIn: Player,
    currentSquad: Player[],
    balance: number
): { valid: boolean; message: string } => {
    if (playerIn.position !== playerOut.position) {
        return {
            valid: false,
            message: "Players must be in the same position.",
        };
    }
    const priceDifference = playerIn.price - playerOut.price;
    if (priceDifference > balance) {
        return {
            valid: false,
            message: `Not enough funds. Need ${priceDifference.toFixed(1)}.`,
        };
    }
    const newSquad = currentSquad.filter((p) => p.id !== playerOut.id);
    newSquad.push(playerIn);
    const teamCounts = newSquad.reduce((acc, p) => {
        if (p.team) acc[p.team] = (acc[p.team] || 0) + 1;
        return acc;
    }, {} as { [key: string]: number });

    if (teamCounts[playerIn.team] > MAX_PLAYERS_PER_TEAM) {
        return {
            valid: false,
            message: `You can't have more than ${MAX_PLAYERS_PER_TEAM} players from ${playerIn.team}.`,
        };
    }
    return { valid: true, message: "" };
};

// --- COMPONENTS ---
const PlayerInfoModal: FC<{ player: Player | null; onClose: () => void }> = ({
    player,
    onClose,
}) => {
    if (!player) return null;
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-md">
                <h2 className="text-2xl font-bold mb-4">{player.name}</h2>
                <p>
                    <span className="font-bold">Position:</span>{" "}
                    {player.position}
                </p>
                <p>
                    <span className="font-bold">Team:</span> {player.team}
                </p>
                <p>
                    <span className="font-bold">Price:</span>{" "}
                    {player.price.toFixed(1)}
                </p>
                <p className="mt-4 text-gray-600">
                    More detailed player statistics and information can be
                    displayed here.
                </p>
                <button
                    onClick={onClose}
                    className="mt-6 bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-700 w-full"
                >
                    Close
                </button>
            </div>
        </div>
    );
};
const MessageBox: FC<{ message: string; type: "error" | "info" }> = ({
    message,
    type,
}) => {
    if (!message) return null;
    const baseStyle = "text-center p-2 my-2 rounded-md font-semibold ";
    const typeStyle =
        type === "error"
            ? "bg-red-200 text-red-800"
            : "bg-blue-200 text-blue-800";
    return <div className={baseStyle + typeStyle}>{message}</div>;
};

const PitchPlayer: FC<{
    player: Player;
    position: { x: number; y: number };
    onClick: (player: Player) => void;
    onInfoClick: (player: Player) => void;
    isSelected: boolean;
}> = ({ player, position, onClick, onInfoClick, isSelected }) => {
    const isPlaceholder =
        typeof player.id === "string" && player.id.startsWith("placeholder");
    const playerStyle: React.CSSProperties = {
        position: "absolute",
        left: `${position.x}%`,
        top: `${position.y}%`,
        transform: "translate(-50%, -50%)",
        cursor: "pointer",
        textAlign: "center",
        width: "80px",
        transition: "all 0.3s ease",
        border: isSelected ? "3px solid #68d391" : "3px solid transparent",
        borderRadius: "8px",
        padding: "2px",
    };

    return (
        <div
            style={playerStyle}
            onClick={() => onClick(player)}
            className="flex flex-col items-center group"
        >
            <div className={`relative w-full`}>
                <div
                    className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center text-white font-bold shadow-lg ${
                        isPlaceholder
                            ? "bg-gray-600 border-2 border-dashed"
                            : player.position === "GK"
                            ? "bg-yellow-500"
                            : "bg-red-600"
                    }`}
                >
                    {isPlaceholder ? "+" : player.position}
                </div>
                <div
                    className={`text-xs font-semibold ${
                        isPlaceholder
                            ? "text-gray-400"
                            : "bg-black bg-opacity-70 text-white"
                    } px-2 py-1 rounded mt-1 whitespace-nowrap`}
                >
                    {player.name}
                </div>
                {!isPlaceholder && (
                    <div className="text-xs font-bold text-white bg-green-600 px-2 rounded-full mt-1">
                        {player.price.toFixed(1)}
                    </div>
                )}
                {!isPlaceholder && (
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onInfoClick(player);
                        }}
                        className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full h-6 w-6 flex items-center justify-center text-xs font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-blue-700"
                        aria-label={`Info for ${player.name}`}
                    >
                        i
                    </button>
                )}
            </div>
        </div>
    );
};

const SquadPitch: FC<{
    players: Player[];
    onPlayerClick: (player: Player) => void;
    onInfoClick: (player: Player) => void;
    selectedPlayerId: number | string | null;
}> = ({ players, onPlayerClick, onInfoClick, selectedPlayerId }) => {
    const positionCounters = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    return (
        <div className="bg-gray-800 p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                Your Squad
            </h2>
            <div
                className="relative bg-green-800 bg-cover w-full mx-auto rounded-lg shadow-lg"
                style={{
                    aspectRatio: "7 / 5",
                    backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill='%232f855a' fill-opacity='0.6'%3E%3Crect x='0' y='0' width='100' height='100'/%3E%3C/g%3E%3Cg fill='%2338a169' fill-opacity='0.6'%3E%3Crect x='0' y='0' width='50' height='100'/%3E%3C/g%3E%3C/svg%3E")`,
                    border: "2px solid white",
                }}
            >
                {players.map((player) => {
                    const posType = player.position;
                    const posIndex = positionCounters[posType];
                    const pitchPosition = squadLayout[posType][posIndex];
                    positionCounters[posType]++;
                    return (
                        <PitchPlayer
                            key={player.id}
                            player={player}
                            position={pitchPosition}
                            onClick={onPlayerClick}
                            onInfoClick={onInfoClick}
                            isSelected={player.id === selectedPlayerId}
                        />
                    );
                })}
            </div>
        </div>
    );
};

const MarketPlayer: FC<{
    player: Player;
    onClick: (player: Player) => void;
    onInfoClick: (player: Player) => void;
    isEligible: boolean;
    isInSquad: boolean;
}> = ({ player, onClick, onInfoClick, isEligible, isInSquad }) => (
    <tr
        onClick={() => isEligible && !isInSquad && onClick(player)}
        className={`border-b border-gray-700 ${
            isInSquad
                ? "bg-gray-800 text-gray-500"
                : isEligible
                ? "bg-blue-900 hover:bg-blue-800 cursor-pointer"
                : "bg-gray-900 text-gray-500"
        }`}
    >
        <td className="p-2 flex items-center space-x-2">
            <button
                onClick={(e) => {
                    e.stopPropagation();
                    onInfoClick(player);
                }}
                className="bg-blue-500 text-white rounded-full h-5 w-5 flex-shrink-0 flex items-center justify-center text-xs font-bold hover:bg-blue-700"
                aria-label={`Info for ${player.name}`}
            >
                i
            </button>
            <span>{player.name}</span>
        </td>
        <td className="p-2">{player.team}</td>
        <td className="p-2">{player.position}</td>
        <td className="p-2 text-right">{player.price.toFixed(1)}</td>
    </tr>
);
const TransferMarket: FC<{
    players: Player[];
    onPlayerClick: (player: Player) => void;
    onInfoClick: (player: Player) => void;
    selectedPlayerId: number | string | null;
    squadPlayers: Player[];
    balance: number;
    filters: any;
    setFilters: any;
}> = ({
    players,
    onPlayerClick,
    onInfoClick,
    selectedPlayerId,
    squadPlayers,
    balance,
    filters,
    setFilters,
}) => {
    const selectedSquadPlayer = squadPlayers.find(
        (p) => p.id === selectedPlayerId
    );
    const squadIds = useMemo(
        () => new Set(squadPlayers.map((p) => p.id)),
        [squadPlayers]
    );
    const filteredPlayers = useMemo(() => {
        return players.filter((p) => {
            if (filters.position && p.position !== filters.position)
                return false;
            if (filters.team && p.team !== filters.team) return false;
            if (filters.maxPrice && p.price > filters.maxPrice) return false;
            return true;
        });
    }, [players, filters]);
    const teams = useMemo(
        () => [...new Set(players.map((p: Player) => p.team))].sort(),
        [players]
    );
    return (
        <div className="bg-gray-800 p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                Transfer Market
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <select
                    value={filters.position}
                    onChange={(e) =>
                        setFilters({ ...filters, position: e.target.value })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                >
                    <option value="">All Positions</option>
                    <option value="GK">GK</option>
                    <option value="DEF">DEF</option>
                    <option value="MID">MID</option>
                    <option value="FWD">FWD</option>
                </select>
                <select
                    value={filters.team}
                    onChange={(e) =>
                        setFilters({ ...filters, team: e.target.value })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                >
                    <option value="">All Teams</option>
                    {teams.map((t: string) => (
                        <option key={t} value={t}>
                            {t}
                        </option>
                    ))}
                </select>
                <input
                    type="number"
                    placeholder="Max Price"
                    value={filters.maxPrice}
                    onChange={(e) =>
                        setFilters({
                            ...filters,
                            maxPrice: e.target.value
                                ? parseFloat(e.target.value)
                                : "",
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                />
                <button
                    onClick={() => setFilters({})}
                    className="bg-red-500 text-white p-2 rounded-md"
                >
                    Reset Filters
                </button>
            </div>
            <div className="overflow-y-auto h-96">
                <table className="w-full text-left text-white">
                    <thead className="sticky top-0 bg-gray-800">
                        <tr>
                            <th className="p-2">Player</th>
                            <th className="p-2">Team</th>
                            <th className="p-2">Pos</th>
                            <th className="p-2 text-right">Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPlayers.map((p) => {
                            const isInSquad = squadIds.has(p.id);
                            const isEligible = selectedSquadPlayer
                                ? validateTransfer(
                                      selectedSquadPlayer,
                                      p,
                                      squadPlayers,
                                      balance
                                  ).valid
                                : false;
                            return (
                                <MarketPlayer
                                    key={p.id}
                                    player={p}
                                    onClick={onPlayerClick}
                                    onInfoClick={onInfoClick}
                                    isEligible={isEligible}
                                    isInSquad={isInSquad}
                                />
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
const BudgetDisplay: FC<{
    squadValue: number;
    balance: number;
    freeTransfers?: number;
    isCreationMode: boolean;
}> = ({ squadValue, balance, freeTransfers, isCreationMode }) => (
    <div
        className={`bg-gray-800 p-3 rounded-lg shadow-inner my-4 grid gap-6 ${
            isCreationMode ? "grid-cols-2" : "grid-cols-3"
        } text-white`}
    >
        <StatsCard
            value={squadValue.toFixed(1)}
            label="Squad Value"
            className="text-center"
        />
        <StatsCard
            value={balance.toFixed(1)}
            label="Remaining Budget"
            className="text-center"
        />
        {!isCreationMode && (
            <StatsCard
                value={freeTransfers ?? 0}
                label="Free Transfers"
                className="text-center"
            />
        )}
    </div>
);

const CaptainSelection: FC<{
    squad: Player[];
    captain: Player | null;
    viceCaptain: Player | null;
    onCaptainSelect: (player: Player) => void;
    onViceCaptainSelect: (player: Player) => void;
}> = ({ squad, captain, viceCaptain, onCaptainSelect, onViceCaptainSelect }) => {
    const realPlayers = squad.filter((p) => typeof p.id === "number");
    
    if (realPlayers.length === 0) {
        return null;
    }

    return (
        <div className="bg-gray-800 rounded-lg p-4 mb-4">
            <h3 className="text-white text-lg font-bold mb-4">Select Captain & Vice-Captain</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Captain Selection */}
                <div>
                    <h4 className="text-white text-md font-semibold mb-2">Captain (2x points)</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                        {realPlayers.map((player) => (
                            <button
                                key={`captain-${player.id}`}
                                onClick={() => onCaptainSelect(player)}
                                className={`w-full text-left p-2 rounded text-sm transition-colors ${
                                    captain?.id === player.id
                                        ? 'bg-yellow-600 text-white'
                                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                }`}
                            >
                                <div className="flex justify-between items-center">
                                    <span>{player.name}</span>
                                    <span className="text-xs">{player.position}</span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Vice-Captain Selection */}
                <div>
                    <h4 className="text-white text-md font-semibold mb-2">Vice-Captain (1.5x points)</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                        {realPlayers.map((player) => (
                            <button
                                key={`vice-captain-${player.id}`}
                                onClick={() => onViceCaptainSelect(player)}
                                className={`w-full text-left p-2 rounded text-sm transition-colors ${
                                    viceCaptain?.id === player.id
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                }`}
                            >
                                <div className="flex justify-between items-center">
                                    <span>{player.name}</span>
                                    <span className="text-xs">{player.position}</span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>
            </div>
            
            {/* Current Selection Display */}
            <div className="mt-4 flex flex-col sm:flex-row gap-4 text-sm">
                <div className="text-white">
                    <span className="font-semibold">Captain:</span> {captain ? captain.name : 'None selected'}
                </div>
                <div className="text-white">
                    <span className="font-semibold">Vice-Captain:</span> {viceCaptain ? viceCaptain.name : 'None selected'}
                </div>
            </div>
        </div>
    );
};

const ConfirmationModal: FC<{
    changes: { playersIn: Player[]; playersOut: Player[] };
    cost: number;
    onConfirm: () => void;
    onCancel: () => void;
}> = ({ changes, cost, onConfirm, onCancel }) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-lg">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">
                    Confirm Transfers
                </h2>
                <div className="space-y-4">
                    {changes.playersIn.length > 0 ? (
                        <>
                            <>
                                <div>
                                    <h3 className="font-bold text-green-600">
                                        Players In
                                    </h3>
                                    <ul>
                                        {changes.playersIn.map((p) => (
                                            <li key={p.id}>
                                                {p.name} ({p.price.toFixed(1)})
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="font-bold text-red-600">
                                        Players Out
                                    </h3>
                                    <ul>
                                        {changes.playersOut.map((p) => (
                                            <li key={p.id}>
                                                {p.name} ({p.price.toFixed(1)})
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </>
                            <div className="border-t pt-4 mt-4">
                                <h3 className="font-bold text-gray-800">
                                    Transfer Cost
                                </h3>
                                <p
                                    className={
                                        cost > 0
                                            ? "text-red-600"
                                            : "text-green-600"
                                    }
                                >
                                    {cost > 0 ? `-${cost} points` : "Free"}
                                </p>
                            </div>
                        </>
                    ) : (
                        <p>No transfers made.</p>
                    )}
                </div>
                <div className="flex justify-end space-x-4 mt-6">
                    <button
                        onClick={onCancel}
                        className="bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-400"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        className="bg-green-500 text-white font-bold py-2 px-4 rounded hover:bg-green-700"
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    );
};

interface FantasyManagerProps {
    isCreationMode?: boolean;
    initialSquadData?: Player[];
    availablePlayers?: Player[];
    initialBudget?: number;
    onSquadSubmit?: (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => void;
}

export default function TransferManager({
    isCreationMode = false,
    initialSquadData = [],
    availablePlayers = [],
    initialBudget = 100.0,
    onSquadSubmit,
}: FantasyManagerProps) {
    const [initialSquadState, setInitialSquadState] = useState<Player[]>(() =>
        isCreationMode ? createPlaceholderSquad() : [...initialSquadData]
    );
    const [squad, setSquad] = useState<Player[]>(
        isCreationMode ? createPlaceholderSquad() : initialSquadData
    );
    const [balance, setBalance] = useState<number>(
        isCreationMode
            ? initialBudget
            : initialBudget // Use the budget value directly from backend
    );
    const [freeTransfers, setFreeTransfers] = useState(0);
    const [initialFreeTransfers, setInitialFreeTransfers] = useState(0);
    const [selectedSquadPlayerId, setSelectedSquadPlayerId] = useState<
        number | string | null
    >(null);
    const [message, setMessage] = useState<string>(
        isCreationMode
            ? "Select a placeholder to add a player."
            : "Select a player from your squad to transfer out."
    );
    const [filters, setFilters] = useState({});
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [modalPlayer, setModalPlayer] = useState<Player | null>(null);
    const [captain, setCaptain] = useState<Player | null>(null);
    const [viceCaptain, setViceCaptain] = useState<Player | null>(null);

    const hasChanges = useMemo(
        () => JSON.stringify(initialSquadState) !== JSON.stringify(squad),
        [initialSquadState, squad]
    );
    const isSquadComplete = useMemo(
        () => isCreationMode && squad.every((p) => typeof p.id === "number"),
        [squad, isCreationMode]
    );

    const getChanges = () => {
        const playersIn = squad.filter(
            (p) => !initialSquadState.find((ip) => ip.id === p.id)
        );
        const playersOut = initialSquadState.filter(
            (isp) => !squad.find((p) => p.id === isp.id)
        );
        return { playersIn, playersOut };
    };

    const handleSquadPlayerClick = (player: Player) => {
        if (selectedSquadPlayerId === player.id) {
            setSelectedSquadPlayerId(null);
            setMessage("Selection cancelled. Select a player to transfer.");
        } else {
            setSelectedSquadPlayerId(player.id);
            const isPlaceholder = typeof player.id === "string";
            setMessage(
                isPlaceholder
                    ? `Adding a ${player.position}. Select a player from the market.`
                    : `Transferring out ${player.name}. Select a player from the market.`
            );
        }
    };

    const handleMarketPlayerClick = (playerIn: Player) => {
        const playerOut = squad.find((p) => p.id === selectedSquadPlayerId);
        if (!playerOut) return;

        const validation = validateTransfer(
            playerOut,
            playerIn,
            squad,
            balance
        );
        if (!validation.valid) {
            setMessage(validation.message);
            return;
        }

        const priceDifference = playerIn.price - playerOut.price;
        setBalance((prev) => prev - priceDifference);

        setSquad((currentSquad) =>
            currentSquad.map((p) => (p.id === playerOut.id ? playerIn : p))
        );

        setMessage(`${playerIn.name} added to squad.`);
        setSelectedSquadPlayerId(null);
    };

    const handleResetClick = () => {
        setSquad(
            isCreationMode
                ? createPlaceholderSquad()
                : JSON.parse(JSON.stringify(initialSquadState))
        );
        setBalance(
            isCreationMode
                ? initialBudget
                : initialBudget // Use the budget value directly from backend
        );
        if (!isCreationMode) setFreeTransfers(initialFreeTransfers);
        setSelectedSquadPlayerId(null);
        setMessage("Changes have been reset.");
    };

    const handleConfirmClick = () => {
        if (isCreationMode) {
            // In creation mode, directly call the final confirm to submit the squad
            handleFinalConfirm();
        } else {
            setShowConfirmationModal(true);
        }
    };

    const handleFinalConfirm = () => {
        const changes = getChanges();
        const transfersMade = changes.playersIn.length;

        if (isCreationMode && onSquadSubmit) {
            // For creation mode, submit the complete squad data
            const realPlayers = squad.filter((p) => typeof p.id === "number");

            onSquadSubmit({
                players: realPlayers,
                captain,
                viceCaptain,
                budget: balance,
            });
        } else if (!isCreationMode && onSquadSubmit) {
            // For transfer mode, submit the transfer data to backend
            const realPlayers = squad.filter((p) => typeof p.id === "number");

            onSquadSubmit({
                players: realPlayers,
                captain,
                viceCaptain,
                budget: balance,
            });
            
            // Update local state after successful submission
            setInitialSquadState(JSON.parse(JSON.stringify(squad)));
            const newFreeTransfers = Math.max(0, freeTransfers - transfersMade);
            setFreeTransfers(newFreeTransfers);
            setInitialFreeTransfers(newFreeTransfers);
        } else {
            // Fallback for when no onSquadSubmit callback is provided
            setInitialSquadState(JSON.parse(JSON.stringify(squad)));
            const newFreeTransfers = Math.max(0, freeTransfers - transfersMade);
            setFreeTransfers(newFreeTransfers);
            setInitialFreeTransfers(newFreeTransfers);
        }

        setShowConfirmationModal(false);
        setMessage(
            isCreationMode
                ? "Squad created successfully!"
                : "Transfers confirmed successfully!"
        );
    };

    const handlePlayerInfoClick = (player: Player) => {
        setModalPlayer(player);
    };

    const handleCaptainSelect = (player: Player) => {
        // If selecting the same player as captain, deselect
        if (captain?.id === player.id) {
            setCaptain(null);
            return;
        }
        
        // If this player is currently vice-captain, remove them from vice-captain
        if (viceCaptain?.id === player.id) {
            setViceCaptain(null);
        }
        
        setCaptain(player);
    };

    const handleViceCaptainSelect = (player: Player) => {
        // If selecting the same player as vice-captain, deselect
        if (viceCaptain?.id === player.id) {
            setViceCaptain(null);
            return;
        }
        
        // If this player is currently captain, remove them from captain
        if (captain?.id === player.id) {
            setCaptain(null);
        }
        
        setViceCaptain(player);
    };

    return (
        <div className="bg-gray-900 min-h-screen p-4 sm:p-8 font-sans pb-24">
            <div className="max-w-7xl mx-auto">
                <BudgetDisplay
                    squadValue={calculateSquadValue(
                        squad.filter((p) => typeof p.id === "number")
                    )}
                    balance={balance}
                    freeTransfers={freeTransfers}
                    isCreationMode={isCreationMode}
                />
                <MessageBox
                    message={message}
                    type={
                        message.includes("Invalid") ||
                        message.includes("Not enough")
                            ? "error"
                            : "info"
                    }
                />

                {/* Captain/Vice-Captain Selection - only show in creation mode when squad is complete */}
                {isCreationMode && isSquadComplete && (
                    <CaptainSelection
                        squad={squad}
                        captain={captain}
                        viceCaptain={viceCaptain}
                        onCaptainSelect={handleCaptainSelect}
                        onViceCaptainSelect={handleViceCaptainSelect}
                    />
                )}

                <div className="flex flex-col lg:flex-row gap-4">
                    <SquadPitch
                        players={squad}
                        onPlayerClick={handleSquadPlayerClick}
                        onInfoClick={handlePlayerInfoClick}
                        selectedPlayerId={selectedSquadPlayerId}
                    />
                    <TransferMarket
                        players={availablePlayers}
                        onPlayerClick={handleMarketPlayerClick}
                        onInfoClick={handlePlayerInfoClick}
                        selectedPlayerId={selectedSquadPlayerId}
                        squadPlayers={squad}
                        balance={balance}
                        filters={filters}
                        setFilters={setFilters}
                    />
                </div>
            </div>
            {(hasChanges || (isCreationMode && isSquadComplete)) && (
                <div className="fixed bottom-0 left-0 right-0 bg-gray-800/50 backdrop-blur supports-[backdrop-filter]:bg-gray-800/30 p-4 flex justify-center space-x-4">
                    <button
                        onClick={handleResetClick}
                        className="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-700 transition-colors"
                    >
                        Reset
                    </button>
                    <button
                        onClick={handleConfirmClick}
                        disabled={isCreationMode && (!isSquadComplete || !captain || !viceCaptain)}
                        className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-500 disabled:cursor-not-allowed"
                    >
                        {isCreationMode ? "Create Team" : "Confirm Transfers"}
                    </button>
                </div>
            )}
            {showConfirmationModal && (
                <ConfirmationModal
                    changes={getChanges()}
                    cost={
                        Math.max(
                            0,
                            getChanges().playersIn.length - freeTransfers
                        ) * POINTS_PER_TRANSFER
                    }
                    onConfirm={handleFinalConfirm}
                    onCancel={() => setShowConfirmationModal(false)}
                />
            )}
            <PlayerInfoModal
                player={modalPlayer}
                onClose={() => setModalPlayer(null)}
            />
        </div>
    );
}
