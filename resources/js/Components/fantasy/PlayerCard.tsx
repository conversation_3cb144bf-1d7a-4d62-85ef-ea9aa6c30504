import React from "react";
import { Card, CardContent } from "@/Components/ui/card";
import { Badge } from "@/Components/ui/badge";
import { Button } from "@/Components/ui/button";

interface PlayerCardProps {
    id: number;
    name: string;
    team: string;
    position: string;
    price: number;
    points: number;
    form?: number;
    ownership?: number;
    selected?: boolean;
    captain?: boolean;
    onSelect?: (id: number) => void;
    onRemove?: (id: number) => void;
    showActions?: boolean;
}

export default function PlayerCard({
    id,
    name,
    team,
    position,
    price,
    points,
    form,
    ownership,
    selected = false,
    captain = false,
    onSelect,
    onRemove,
    showActions = true
}: PlayerCardProps) {
    const getPositionColor = (pos: string) => {
        switch (pos.toLowerCase()) {
            case 'goalkeeper':
                return 'bg-yellow-100 text-yellow-800';
            case 'defender':
                return 'bg-green-100 text-green-800';
            case 'midfielder':
                return 'bg-blue-100 text-blue-800';
            case 'forward':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <Card className={`${selected ? 'ring-2 ring-blue-500' : ''} ${captain ? 'ring-2 ring-yellow-500' : ''}`}>
            <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                            {team}
                        </Badge>
                        <Badge className={`text-xs ${getPositionColor(position)}`}>
                            {position}
                        </Badge>
                        {captain && (
                            <Badge className="text-xs bg-yellow-100 text-yellow-800">
                                (C)
                            </Badge>
                        )}
                    </div>
                    <div className="text-lg font-bold text-green-600">
                        £{price.toFixed(1)}m
                    </div>
                </div>
                
                <h3 className="font-semibold text-slate-900 mb-2">{name}</h3>
                
                <div className="grid grid-cols-2 gap-2 text-sm text-slate-600 mb-3">
                    <div>Points: <span className="font-medium">{points}</span></div>
                    {form && <div>Form: <span className="font-medium">{form}</span></div>}
                    {ownership && <div>Owned: <span className="font-medium">{ownership}%</span></div>}
                </div>
                
                {showActions && (
                    <div className="flex gap-2">
                        {selected ? (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onRemove?.(id)}
                                className="flex-1"
                            >
                                Remove
                            </Button>
                        ) : (
                            <Button
                                size="sm"
                                onClick={() => onSelect?.(id)}
                                className="flex-1"
                            >
                                Select
                            </Button>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
