import React from "react";
import { Badge } from "@/Components/ui/badge";

interface FixtureItemProps {
    homeTeam: string;
    awayTeam: string;
    kickoff: string;
    gameweek?: string;
    homeScore?: number | null;
    awayScore?: number | null;
    status?: "upcoming" | "finished" | "live";
}

export default function FixtureItem({
    homeTeam,
    awayTeam,
    kickoff,
    gameweek,
    homeScore,
    awayScore,
    status = "upcoming"
}: FixtureItemProps) {
    const formatKickoff = (kickoffTime: string) => {
        const date = new Date(kickoffTime);
        return date.toLocaleDateString('en-GB', {
            weekday: 'short',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-100">
            <div className="space-y-1">
                <p className="font-medium text-slate-900">
                    {homeTeam} {status === "finished" && homeScore !== null && awayScore !== null 
                        ? `${homeScore}-${awayScore}` 
                        : "vs"} {awayTeam}
                </p>
                <p className="text-sm text-slate-600">
                    {formatKickoff(kickoff)}
                </p>
            </div>
            {gameweek && (
                <Badge variant="outline">{gameweek}</Badge>
            )}
        </div>
    );
}
