import React, { useState, FC, useEffect, useRef, useMemo } from "react";
import { router } from "@inertiajs/react";
import StatsCard from "./StatsCard";

// --- TYPE DEFINITIONS ---
interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}

interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
}

interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

interface MyTeamManagerProps {
    teamData: TeamData;
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    currentGameweek: CurrentGameweek;
}
interface Player {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    onPitch: boolean;
    pitchPosition: { x: number; y: number } | null;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

interface PositionLayout {
    x: number;
    y: number;
}

interface FormationLayout {
    GK: PositionLayout[];
    DEF: PositionLayout[];
    MID: PositionLayout[];
    FWD: PositionLayout[];
}

interface Formations {
    [key: string]: FormationLayout;
}

// --- CONSTANTS ---

const formations: Formations = {
    "4-4-2": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 20, y: 70 },
            { x: 40, y: 70 },
            { x: 60, y: 70 },
            { x: 80, y: 70 },
        ],
        MID: [
            { x: 20, y: 50 },
            { x: 40, y: 50 },
            { x: 60, y: 50 },
            { x: 80, y: 50 },
        ],
        FWD: [
            { x: 40, y: 30 },
            { x: 60, y: 30 },
        ],
    },
    "4-3-3": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 20, y: 70 },
            { x: 40, y: 70 },
            { x: 60, y: 70 },
            { x: 80, y: 70 },
        ],
        MID: [
            { x: 30, y: 50 },
            { x: 50, y: 50 },
            { x: 70, y: 50 },
        ],
        FWD: [
            { x: 25, y: 30 },
            { x: 50, y: 30 },
            { x: 75, y: 30 },
        ],
    },
    "3-5-2": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 30, y: 70 },
            { x: 50, y: 70 },
            { x: 70, y: 70 },
        ],
        MID: [
            { x: 15, y: 50 },
            { x: 35, y: 50 },
            { x: 50, y: 50 },
            { x: 65, y: 50 },
            { x: 85, y: 50 },
        ],
        FWD: [
            { x: 40, y: 30 },
            { x: 60, y: 30 },
        ],
    },
    "5-3-2": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 15, y: 70 },
            { x: 30, y: 70 },
            { x: 50, y: 70 },
            { x: 70, y: 70 },
            { x: 85, y: 70 },
        ],
        MID: [
            { x: 30, y: 50 },
            { x: 50, y: 50 },
            { x: 70, y: 50 },
        ],
        FWD: [
            { x: 40, y: 30 },
            { x: 60, y: 30 },
        ],
    },
    "4-5-1": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 20, y: 70 },
            { x: 40, y: 70 },
            { x: 60, y: 70 },
            { x: 80, y: 70 },
        ],
        MID: [
            { x: 15, y: 50 },
            { x: 35, y: 50 },
            { x: 50, y: 50 },
            { x: 65, y: 50 },
            { x: 85, y: 50 },
        ],
        FWD: [{ x: 50, y: 30 }],
    },
    "3-4-3": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 30, y: 70 },
            { x: 50, y: 70 },
            { x: 70, y: 70 },
        ],
        MID: [
            { x: 20, y: 50 },
            { x: 40, y: 50 },
            { x: 60, y: 50 },
            { x: 80, y: 50 },
        ],
        FWD: [
            { x: 25, y: 30 },
            { x: 50, y: 30 },
            { x: 75, y: 30 },
        ],
    },
    "5-4-1": {
        GK: [{ x: 50, y: 90 }],
        DEF: [
            { x: 15, y: 70 },
            { x: 30, y: 70 },
            { x: 50, y: 70 },
            { x: 70, y: 70 },
            { x: 85, y: 70 },
        ],
        MID: [
            { x: 20, y: 50 },
            { x: 40, y: 50 },
            { x: 60, y: 50 },
            { x: 80, y: 50 },
        ],
        FWD: [{ x: 50, y: 30 }],
    },
};

// --- UTILITY & VALIDATION ---
const calculateSquadValue = (
    squad: Omit<Player, "onPitch" | "pitchPosition" | "initialOnPitch">[]
): number => {
    return squad.reduce((total, player) => total + player.price, 0);
};

const getFormationKey = (counts: { [key: string]: number }): string =>
    `${counts.DEF}-${counts.MID}-${counts.FWD}`;

const applyFormation = (players: Player[], formationKey: string): Player[] => {
    let formationLayout = formations[formationKey];
    
    // If formation doesn't exist, use 4-4-2 as fallback
    if (!formationLayout) {
        console.warn(`Formation ${formationKey} not found, using 4-4-2 fallback`);
        formationLayout = formations["4-4-2"];
    }

    const positionCounters = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    const playersWithPositions = [...players];

    // First, position players according to the formation
    playersWithPositions
        .filter((p) => p.onPitch)
        .forEach((player) => {
            const posType = player.position;
            const posIndex = positionCounters[posType];
            
            if (
                formationLayout[posType] &&
                posIndex < formationLayout[posType].length
            ) {
                player.pitchPosition = formationLayout[posType][posIndex];
                positionCounters[posType]++;
            } else {
                // Fallback positioning for extra players or invalid formations
                console.warn(`No formation position available for ${player.name} (${posType}), using fallback`);
                player.pitchPosition = { 
                    x: 50 + (posIndex * 10) - 20, // Spread horizontally
                    y: posType === 'GK' ? 90 : posType === 'DEF' ? 70 : posType === 'MID' ? 50 : 30 
                };
            }
        });

    return playersWithPositions;
};

const initializeTeam = (
    squad: Omit<Player, "onPitch" | "pitchPosition">[],
    startingIds: number[]
): Player[] => {
    let players: Player[] = squad.map((player) => ({
        ...player,
        onPitch: startingIds.includes(player.id),
        pitchPosition: null,
    }));
    const pitchPlayers = players.filter((p) => p.onPitch);
    const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    pitchPlayers.forEach((p) => {
        counts[p.position]++;
    });

    const formationKey = getFormationKey(counts);
    return applyFormation(players, formationKey);
};

const validateSwap = (
    playerOut: Player,
    playerIn: Player,
    currentPitchPlayers: Player[],
    balance: number
): { valid: boolean; message: string } => {
    const priceDifference = playerIn.price - playerOut.price;
    if (priceDifference > balance)
        return {
            valid: false,
            message: `Not enough funds. Need ${priceDifference.toFixed(1)}.`,
        };
    const newPitchLineup = currentPitchPlayers.filter(
        (p) => p.id !== playerOut.id
    );
    newPitchLineup.push(playerIn);
    const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    newPitchLineup.forEach((p) => {
        counts[p.position]++;
    });
    if (counts.GK !== 1)
        return {
            valid: false,
            message: "Team must have exactly one Goalkeeper.",
        };
    if (counts.DEF < 3 || counts.DEF > 5)
        return { valid: false, message: "Team must have 3 to 5 Defenders." };
    if (counts.MID < 3 || counts.MID > 5)
        return { valid: false, message: "Team must have 3 to 5 Midfielders." };
    if (counts.FWD < 1 || counts.FWD > 3)
        return { valid: false, message: "Team must have 1 to 3 Forwards." };
    const formationKey = getFormationKey(counts);
    if (!formations[formationKey])
        return { valid: false, message: `Invalid formation: ${formationKey}.` };
    return { valid: true, message: "" };
};

// --- COMPONENTS ---
const PlayerInfoModal: FC<{ player: Player | null; onClose: () => void }> = ({
    player,
    onClose,
}) => {
    if (!player) return null;
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-md">
                <h2 className="text-2xl font-bold mb-4">{player.name}</h2>
                <p>
                    <span className="font-bold">Position:</span>{" "}
                    {player.position}
                </p>
                <p>
                    <span className="font-bold">Price:</span>{" "}
                    {player.price.toFixed(1)}
                </p>
                <p>
                    <span className="font-bold">Status:</span>{" "}
                    {player.onPitch ? "On Pitch" : "On Bench"}
                </p>
                <p className="mt-4 text-gray-600">
                    More detailed player statistics and information can be
                    displayed here.
                </p>
                <button
                    onClick={onClose}
                    className="mt-6 bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-700 w-full"
                >
                    Close
                </button>
            </div>
        </div>
    );
};
const PlayerComponent: FC<{
    player: Player;
    isBench?: boolean;
    onClick: (player: Player, element: HTMLElement) => void;
    onInfoClick: (player: Player) => void;
    isSelected: boolean;
    isEligible?: boolean;
}> = ({ player, isBench, onClick, onInfoClick, isSelected, isEligible }) => {
    const playerRef = useRef<HTMLDivElement>(null);
    let borderStyle = "3px solid transparent";
    if (isSelected) borderStyle = "3px solid #68d391";
    else if (isEligible) borderStyle = "3px solid #63b3ed";
    const playerStyle: React.CSSProperties = {
        position: isBench ? "relative" : "absolute",
        left: isBench ? "auto" : `${player.pitchPosition?.x}%`,
        top: isBench ? "auto" : `${player.pitchPosition?.y}%`,
        transform: isBench ? "none" : "translate(-50%, -50%)",
        textAlign: "center",
        width: isBench ? "80px" : "60px",
        transition: "all 0.3s ease",
        borderRadius: "8px",
        padding: "2px",
    };
    return (
        <div
            ref={playerRef}
            style={playerStyle}
            className="flex flex-col items-center group"
        >
            <div
                onClick={() =>
                    playerRef.current && onClick(player, playerRef.current)
                }
                style={{
                    border: borderStyle,
                    borderRadius: "8px",
                    cursor: "pointer",
                    padding: "2px",
                }}
                className={`relative transition-opacity duration-300 ${
                    isSelected || isEligible || isEligible === undefined
                        ? "opacity-100"
                        : "opacity-50"
                }`}
            >
                <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold shadow-lg ${
                        player.position === "GK"
                            ? "bg-yellow-500"
                            : "bg-red-600"
                    }`}
                >
                    {player.position}
                </div>
                {player.is_captain && (
                    <div className="absolute -top-1 -left-1 bg-purple-600 text-white text-xs font-bold w-5 h-5 rounded-full flex items-center justify-center border-2 border-white">
                        C
                    </div>
                )}
                {player.is_vice_captain && (
                    <div className="absolute -top-1 -left-1 bg-gray-600 text-white text-xs font-bold w-5 h-5 rounded-full flex items-center justify-center border-2 border-white">
                        VC
                    </div>
                )}
                <div className="text-xs font-semibold bg-black bg-opacity-70 text-white px-2 py-1 rounded mt-1 whitespace-nowrap">
                    {player.name}
                </div>
                <div className="text-xs font-bold text-white bg-green-600 px-2 rounded-full mt-1">
                    {player.price.toFixed(1)}
                </div>
            </div>
            <button
                onClick={(e) => {
                    e.stopPropagation();
                    onInfoClick(player);
                }}
                className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full h-6 w-6 flex items-center justify-center text-xs font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-blue-700"
                aria-label={`Info for ${player.name}`}
            >
                i
            </button>
        </div>
    );
};
interface PitchAndBenchProps {
    players: Player[];
    onPlayerClick: (player: Player, element: HTMLElement) => void;
    onPlayerInfoClick: (player: Player) => void;
    selectedPlayerId: number | null;
    eligiblePlayerIds: number[] | null;
}
const Pitch: FC<PitchAndBenchProps> = ({
    players,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    eligiblePlayerIds,
}) => (
    <div
        className="relative bg-green-800 bg-cover w-full max-w-2xl mx-auto rounded-lg shadow-lg"
        style={{
            aspectRatio: "7 / 5",
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill='%232f855a' fill-opacity='0.6'%3E%3Crect x='0' y='0' width='100' height='100'/%3E%3C/g%3E%3Cg fill='%2338a169' fill-opacity='0.6'%3E%3Crect x='0' y='0' width='50' height='100'/%3E%3C/g%3E%3C/svg%3E")`,
            border: "2px solid white",
        }}
    >
        <div className="absolute top-1/2 left-1/2 w-32 h-32 border-2 border-white/50 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-0 left-1/2 w-0.5 h-full bg-white/50"></div>
        <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-1/2 left-0 -translate-y-1/2 w-24 h-48 border-2 border-white/50 border-l-0 rounded-r-lg"></div>
        <div className="absolute top-1/2 right-0 -translate-y-1/2 w-24 h-48 border-2 border-white/50 border-r-0 rounded-l-lg"></div>
        {players
            .filter((p) => p.onPitch)
            .map((player) => (
                <PlayerComponent
                    key={player.id}
                    player={player}
                    onClick={onPlayerClick}
                    onInfoClick={onPlayerInfoClick}
                    isSelected={player.id === selectedPlayerId}
                    isEligible={eligiblePlayerIds?.includes(player.id)}
                />
            ))}
    </div>
);
const Bench: FC<PitchAndBenchProps> = ({
    players,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    eligiblePlayerIds,
}) => (
    <div className="bg-gray-800 p-4 mt-4 rounded-lg shadow-inner">
        <h3 className="text-white text-center font-bold mb-2">Bench</h3>
        <div className="flex flex-wrap justify-center gap-4">
            {players
                .filter((p) => !p.onPitch)
                .map((player) => (
                    <PlayerComponent
                        key={player.id}
                        player={player}
                        isBench
                        onClick={onPlayerClick}
                        onInfoClick={onPlayerInfoClick}
                        isSelected={player.id === selectedPlayerId}
                        isEligible={eligiblePlayerIds?.includes(player.id)}
                    />
                ))}
        </div>
    </div>
);
const MessageBox: FC<{ message: string; type: "error" | "info" }> = ({
    message,
    type,
}) => {
    if (!message) return null;
    const baseStyle = "text-center p-2 my-2 rounded-md font-semibold ";
    const typeStyle =
        type === "error"
            ? "bg-red-200 text-red-800"
            : "bg-blue-200 text-blue-800";
    return <div className={baseStyle + typeStyle}>{message}</div>;
};
const ActionMenu: FC<{
    player: Player;
    position: { top: number; left: number };
    onClose: () => void;
    onInitiateSwap: (player: Player) => void;
    onSetCaptain: (player: Player) => void;
    onSetViceCaptain: (player: Player) => void;
}> = ({
    player,
    position,
    onClose,
    onInitiateSwap,
    onSetCaptain,
    onSetViceCaptain,
}) => {
    const menuRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                menuRef.current &&
                !menuRef.current.contains(event.target as Node)
            ) {
                onClose();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () =>
            document.removeEventListener("mousedown", handleClickOutside);
    }, [onClose]);
    return (
        <div
            ref={menuRef}
            style={{ top: position.top, left: position.left }}
            className="absolute bg-white rounded-md shadow-lg z-20 text-sm"
        >
            <ul className="py-1">
                <li
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => onInitiateSwap(player)}
                >
                    Substitute
                </li>
                {player.onPitch && !player.is_captain && (
                    <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => onSetCaptain(player)}
                    >
                        Make Captain
                    </li>
                )}
                {player.onPitch && !player.is_vice_captain && (
                    <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => onSetViceCaptain(player)}
                    >
                        Make Vice-Captain
                    </li>
                )}
            </ul>
        </div>
    );
};
const BudgetDisplay: FC<{ squadValue: number; balance: number }> = ({
    squadValue,
    balance,
}) => (
    <div className="bg-gray-800 p-3 rounded-lg shadow-inner my-4 grid gap-6 grid-cols-2">
        <StatsCard
            value={squadValue.toFixed(1)}
            label="Squad Value"
            className="text-center"
        />
        <StatsCard
            value={balance.toFixed(1)}
            label="Remaining Budget"
            className="text-center"
        />
    </div>
);

interface ConfirmationModalProps {
    changes: {
        playersIn: Player[];
        playersOut: Player[];
        captain?: Player;
        viceCaptain?: Player;
    };
    onConfirm: () => void;
    onCancel: () => void;
}

const ConfirmationModal: FC<ConfirmationModalProps> = ({
    changes,
    onConfirm,
    onCancel,
}) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-lg">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">
                    Confirm Changes
                </h2>
                <div className="space-y-4">
                    {changes.playersIn.length > 0 && (
                        <div>
                            <h3 className="font-bold text-green-600">
                                Players In
                            </h3>
                            <ul>
                                {changes.playersIn.map((p) => (
                                    <li key={p.id}>{p.name}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {changes.playersOut.length > 0 && (
                        <div>
                            <h3 className="font-bold text-red-600">
                                Players Out
                            </h3>
                            <ul>
                                {changes.playersOut.map((p) => (
                                    <li key={p.id}>{p.name}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {changes.captain && (
                        <div>
                            <h3 className="font-bold text-purple-600">
                                New Captain
                            </h3>
                            <p>{changes.captain.name}</p>
                        </div>
                    )}
                    {changes.viceCaptain && (
                        <div>
                            <h3 className="font-bold text-gray-600">
                                New Vice-Captain
                            </h3>
                            <p>{changes.viceCaptain.name}</p>
                        </div>
                    )}
                </div>
                <div className="flex justify-end space-x-4 mt-6">
                    <button
                        onClick={onCancel}
                        className="bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-400"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        className="bg-green-500 text-white font-bold py-2 px-4 rounded hover:bg-green-700"
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    );
};

export default function MyTeamManager({
    teamData,
    squadPlayers,
    startingPlayerIds,
    fantasyTeam,
    currentGameweek,
}: MyTeamManagerProps) {
    // Convert squadPlayers to Player format for internal use
    const initialSquad = squadPlayers.map(player => ({
        id: player.id,
        name: player.name,
        position: player.position,
        price: player.price,
        is_captain: player.is_captain,
        is_vice_captain: player.is_vice_captain,
        onPitch: false, // Will be set by initializeTeam
        pitchPosition: null, // Will be set by initializeTeam
    }));

    const [initialPlayersState, setInitialPlayersState] = useState<Player[]>(
        () => {
            console.log('Debug - squadPlayers:', squadPlayers);
            console.log('Debug - startingPlayerIds:', startingPlayerIds);
            console.log('Debug - initialSquad:', initialSquad);
            const result = initializeTeam(initialSquad, startingPlayerIds);
            console.log('Debug - initialized players:', result);
            return result;
        }
    );
    const [players, setPlayers] = useState<Player[]>(() =>
        JSON.parse(JSON.stringify(initialPlayersState))
    );
    const [balance, setBalance] = useState<number>(teamData.balance);
    const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(
        null
    );
    const [eligiblePlayerIds, setEligiblePlayerIds] = useState<number[] | null>(
        null
    );
    const [message, setMessage] = useState<string>(
        "Click a player for options."
    );
    const [modalPlayer, setModalPlayer] = useState<Player | null>(null);
    const [actionMenu, setActionMenu] = useState<{
        player: Player;
        position: { top: number; left: number };
    } | null>(null);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);

    const squadValue = useMemo(() => calculateSquadValue(players), [players]);
    const hasChanges = useMemo(
        () => JSON.stringify(initialPlayersState) !== JSON.stringify(players),
        [initialPlayersState, players]
    );

    const getChanges = () => {
        const playersIn = players.filter(
            (p) =>
                p.onPitch &&
                !initialPlayersState.find((ip) => ip.id === p.id)?.onPitch
        );
        const playersOut = players.filter(
            (p) =>
                !p.onPitch &&
                initialPlayersState.find((ip) => ip.id === p.id)?.onPitch
        );
        const newCaptain = players.find(
            (p) =>
                p.is_captain &&
                !initialPlayersState.find((ip) => ip.id === p.id)?.is_captain
        );
        const newViceCaptain = players.find(
            (p) =>
                p.is_vice_captain &&
                !initialPlayersState.find((ip) => ip.id === p.id)
                    ?.is_vice_captain
        );
        return {
            playersIn,
            playersOut,
            captain: newCaptain,
            viceCaptain: newViceCaptain,
        };
    };

    const handlePlayerClick = (clickedPlayer: Player, element: HTMLElement) => {
        if (selectedPlayerId) {
            handleSwapSelection(clickedPlayer);
        } else {
            const rect = element.getBoundingClientRect();
            setActionMenu({
                player: clickedPlayer,
                position: {
                    top: rect.bottom + window.scrollY,
                    left: rect.left + window.scrollX,
                },
            });
        }
    };
    const handleInitiateSwap = (playerToSwap: Player) => {
        setActionMenu(null);
        setMessage(
            `Selected ${playerToSwap.name}. Choose an eligible player to swap.`
        );
        setSelectedPlayerId(playerToSwap.id);
        const pitchPlayers = players.filter((p) => p.onPitch);
        const potentialSwaps = players.filter(
            (p) => p.onPitch !== playerToSwap.onPitch
        );
        const eligible = potentialSwaps
            .filter((p) => {
                const playerOut = playerToSwap.onPitch ? playerToSwap : p;
                const playerIn = playerToSwap.onPitch ? p : playerToSwap;
                return validateSwap(playerOut, playerIn, pitchPlayers, balance)
                    .valid;
            })
            .map((p) => p.id);
        setEligiblePlayerIds(eligible);
    };
    const handleSwapSelection = (clickedPlayer: Player) => {
        const selectedPlayer = players.find((p) => p.id === selectedPlayerId);
        if (!selectedPlayer) return;
        if (selectedPlayer.id === clickedPlayer.id) {
            setSelectedPlayerId(null);
            setEligiblePlayerIds(null);
            setMessage("Selection cancelled.");
            return;
        }
        if (!eligiblePlayerIds?.includes(clickedPlayer.id)) {
            setMessage("Invalid swap. Please select a highlighted player.");
            setSelectedPlayerId(null);
            setEligiblePlayerIds(null);
            return;
        }
        const playerOut = selectedPlayer.onPitch
            ? selectedPlayer
            : clickedPlayer;
        const playerIn = selectedPlayer.onPitch
            ? clickedPlayer
            : selectedPlayer;
        const priceDifference = playerIn.price - playerOut.price;
        setBalance((prev) => prev - priceDifference);
        setPlayers((currentPlayers) => {
            let newPlayers = JSON.parse(JSON.stringify(currentPlayers));
            const p1 = newPlayers.find((p: Player) => p.id === playerIn.id);
            const p2 = newPlayers.find((p: Player) => p.id === playerOut.id);
            if (p1) p1.onPitch = true;
            if (p2) {
                p2.onPitch = false;
                if (p2.is_captain) {
                    p2.is_captain = false;
                    if (p1) p1.is_captain = true;
                }
                if (p2.is_vice_captain) {
                    p2.is_vice_captain = false;
                    if (p1) p1.is_vice_captain = true;
                }
            }
            const newPitchPlayers = newPlayers.filter((p: Player) => p.onPitch);
            const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
            newPitchPlayers.forEach((p: Player) => {
                counts[p.position]++;
            });
            const formationKey = getFormationKey(counts);
            return applyFormation(newPlayers, formationKey);
        });
        setMessage(
            `${playerIn.name} has been substituted for ${playerOut.name}.`
        );
        setSelectedPlayerId(null);
        setEligiblePlayerIds(null);
    };
    const handleSetCaptain = (playerToMakeCaptain: Player) => {
        setPlayers((currentPlayers) => {
            const oldCaptain = currentPlayers.find((p) => p.is_captain);
            return currentPlayers.map((p) => {
                if (p.id === playerToMakeCaptain.id)
                    return { ...p, is_captain: true, is_vice_captain: false };
                if (oldCaptain && p.id === oldCaptain.id)
                    return { ...p, is_captain: false, is_vice_captain: true };
                if (
                    oldCaptain &&
                    p.id !== playerToMakeCaptain.id &&
                    p.id !== oldCaptain.id
                )
                    return { ...p, is_vice_captain: false };
                return p;
            });
        });
        setActionMenu(null);
    };
    const handleSetViceCaptain = (playerToMakeViceCaptain: Player) => {
        setPlayers((currentPlayers) => {
            const oldCaptain = currentPlayers.find((p) => p.is_captain);
            if (oldCaptain && playerToMakeViceCaptain.id === oldCaptain.id) {
                return currentPlayers.map((p) => {
                    if (p.is_captain)
                        return {
                            ...p,
                            is_captain: false,
                            is_vice_captain: true,
                        };
                    if (p.is_vice_captain)
                        return {
                            ...p,
                            is_captain: true,
                            is_vice_captain: false,
                        };
                    return p;
                });
            }
            return currentPlayers.map((p) => {
                if (p.id === playerToMakeViceCaptain.id)
                    return { ...p, is_vice_captain: true };
                if (p.is_vice_captain) return { ...p, is_vice_captain: false };
                return p;
            });
        });
        setActionMenu(null);
    };
    const handlePlayerInfoClick = (player: Player) => setModalPlayer(player);
    const handleCloseModal = () => setModalPlayer(null);

    const handleConfirmClick = () => {
        setShowConfirmationModal(true);
    };

    const handleFinalConfirm = () => {
        // Get current formation
        const pitchPlayers = players.filter(p => p.onPitch);
        const formationCounts = pitchPlayers.reduce(
            (acc, player) => {
                acc[player.position] = (acc[player.position] || 0) + 1;
                return acc;
            },
            {} as { [key: string]: number }
        );
        const currentFormation = getFormationKey(formationCounts);
        
        // Get lineup data
        const lineupPlayerIds = pitchPlayers.map(p => p.id);
        const captain = players.find(p => p.is_captain);
        const viceCaptain = players.find(p => p.is_vice_captain);
        
        if (!captain || !viceCaptain) {
            setMessage("Please select both captain and vice captain.");
            return;
        }
        
        // Save to backend
        router.post('/my-team/update', {
            lineup_players: lineupPlayerIds,
            captain_id: captain.id,
            vice_captain_id: viceCaptain.id,
            formation: currentFormation,
        }, {
            onSuccess: () => {
                setInitialPlayersState(JSON.parse(JSON.stringify(players)));
                setShowConfirmationModal(false);
                setMessage("Changes saved successfully!");
            },
            onError: (errors) => {
                console.error('Save failed:', errors);
                setMessage("Failed to save changes. Please try again.");
            }
        });
    };

    const handleResetClick = () => {
        setPlayers(JSON.parse(JSON.stringify(initialPlayersState)));
        setBalance(teamData.balance);
        setSelectedPlayerId(null);
        setEligiblePlayerIds(null);
        setActionMenu(null);
        setMessage("Changes have been reset. Click a player for options.");
    };

    return (
        <div className="bg-gray-900 min-h-screen p-4 sm:p-8 font-sans pb-24">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-white text-center mb-2">
                    Fantasy Team Manager
                </h1>
                <BudgetDisplay squadValue={teamData.squadValue} balance={balance} />
                <MessageBox
                    message={message}
                    type={
                        message.includes("Invalid") ||
                        message.includes("must") ||
                        message.includes("Not enough")
                            ? "error"
                            : "info"
                    }
                />
                {actionMenu && (
                    <ActionMenu
                        player={actionMenu.player}
                        position={actionMenu.position}
                        onClose={() => setActionMenu(null)}
                        onInitiateSwap={handleInitiateSwap}
                        onSetCaptain={handleSetCaptain}
                        onSetViceCaptain={handleSetViceCaptain}
                    />
                )}
                <Pitch
                    players={players}
                    onPlayerClick={handlePlayerClick}
                    onPlayerInfoClick={handlePlayerInfoClick}
                    selectedPlayerId={selectedPlayerId}
                    eligiblePlayerIds={eligiblePlayerIds}
                />
                <Bench
                    players={players}
                    onPlayerClick={handlePlayerClick}
                    onPlayerInfoClick={handlePlayerInfoClick}
                    selectedPlayerId={selectedPlayerId}
                    eligiblePlayerIds={eligiblePlayerIds}
                />
                <PlayerInfoModal
                    player={modalPlayer}
                    onClose={handleCloseModal}
                />
                {showConfirmationModal && (
                    <ConfirmationModal
                        changes={getChanges()}
                        onConfirm={handleFinalConfirm}
                        onCancel={() => setShowConfirmationModal(false)}
                    />
                )}
            </div>
            {hasChanges && (
                <div className="fixed bottom-0 left-0 right-0 bg-gray-800 p-4 flex justify-center space-x-4">
                    <button
                        onClick={handleResetClick}
                        className="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-700 transition-colors"
                    >
                        Reset
                    </button>
                    <button
                        onClick={handleConfirmClick}
                        className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors"
                    >
                        Confirm Changes
                    </button>
                </div>
            )}
        </div>
    );
}
