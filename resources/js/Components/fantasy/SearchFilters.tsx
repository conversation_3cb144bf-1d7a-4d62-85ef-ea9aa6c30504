import React from "react";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";

interface SearchFiltersProps {
    searchTerm: string;
    onSearchChange: (value: string) => void;
    selectedPosition: string;
    onPositionChange: (value: string) => void;
    priceRange?: [number, number];
    onPriceRangeChange?: (value: [number, number]) => void;
}

export default function SearchFilters({
    searchTerm,
    onSearchChange,
    selectedPosition,
    onPositionChange,
    priceRange,
    onPriceRangeChange
}: SearchFiltersProps) {
    return (
        <div className={`grid grid-cols-1 gap-4 mb-6 ${priceRange ? 'md:grid-cols-4' : 'md:grid-cols-2'}`}>
            <div className="space-y-2">
                <Label htmlFor="search">Search Players</Label>
                <Input
                    id="search"
                    placeholder="Search by name or team..."
                    value={searchTerm}
                    onChange={(e) => onSearchChange(e.target.value)}
                />
            </div>
            
            <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Select value={selectedPosition} onValueChange={onPositionChange}>
                    <SelectTrigger id="position">
                        <SelectValue placeholder="All positions" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Positions</SelectItem>
                        <SelectItem value="goalkeeper">Goalkeeper</SelectItem>
                        <SelectItem value="defender">Defender</SelectItem>
                        <SelectItem value="midfielder">Midfielder</SelectItem>
                        <SelectItem value="forward">Forward</SelectItem>
                    </SelectContent>
                </Select>
            </div>
            
            {priceRange && onPriceRangeChange && (
                <>
                    <div className="space-y-2">
                        <Label htmlFor="minPrice">Min Price</Label>
                        <Input
                            id="minPrice"
                            type="number"
                            step="0.1"
                            value={priceRange[0]}
                            onChange={(e) => {
                                const newValue = parseFloat(e.target.value) || 0;
                                onPriceRangeChange([newValue, priceRange[1]]);
                            }}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="maxPrice">Max Price</Label>
                        <Input
                            id="maxPrice"
                            type="number"
                            step="0.1"
                            value={priceRange[1]}
                            onChange={(e) => {
                                const newValue = parseFloat(e.target.value) || 0;
                                onPriceRangeChange([priceRange[0], newValue]);
                            }}
                        />
                    </div>
                </>
            )}
        </div>
    );
}
