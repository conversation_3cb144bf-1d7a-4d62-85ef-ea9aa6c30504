import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/Components/ui/card";
import { Badge } from "@/Components/ui/badge";
import { Button } from "@/Components/ui/button";

interface LeagueCardProps {
    id: number;
    name: string;
    type: "Private" | "Public";
    members: number;
    rank?: number;
    points?: number;
    prize?: string;
    entryFee?: string;
    code?: string;
    isJoined?: boolean;
    onJoin?: (id: number) => void;
    onLeave?: (id: number) => void;
}

export default function LeagueCard({
    id,
    name,
    type,
    members,
    rank,
    points,
    prize,
    entryFee,
    code,
    isJoined = false,
    onJoin,
    onLeave
}: LeagueCardProps) {
    return (
        <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{name}</CardTitle>
                    <Badge variant={type === "Private" ? "secondary" : "default"}>
                        {type}
                    </Badge>
                </div>
            </CardHeader>
            <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span className="text-slate-600">Members:</span>
                        <span className="font-medium ml-1">{members.toLocaleString()}</span>
                    </div>
                    {rank && (
                        <div>
                            <span className="text-slate-600">Rank:</span>
                            <span className="font-medium ml-1">{rank}</span>
                        </div>
                    )}
                    {points && (
                        <div>
                            <span className="text-slate-600">Points:</span>
                            <span className="font-medium ml-1">{points}</span>
                        </div>
                    )}
                    {entryFee && (
                        <div>
                            <span className="text-slate-600">Entry:</span>
                            <span className="font-medium ml-1">{entryFee}</span>
                        </div>
                    )}
                </div>
                
                {prize && (
                    <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div className="text-sm">
                            <span className="text-yellow-800 font-medium">Prize: </span>
                            <span className="text-yellow-700">{prize}</span>
                        </div>
                    </div>
                )}
                
                {code && (
                    <div className="p-2 bg-slate-100 rounded text-center">
                        <span className="text-xs text-slate-600">Code: </span>
                        <span className="font-mono font-medium">{code}</span>
                    </div>
                )}
                
                <div className="pt-2">
                    {isJoined ? (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onLeave?.(id)}
                            className="w-full"
                        >
                            Leave League
                        </Button>
                    ) : (
                        <Button
                            size="sm"
                            onClick={() => onJoin?.(id)}
                            className="w-full"
                        >
                            Join League
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
