import React from "react";
import { Card, CardContent } from "@/Components/ui/card";

interface HeroSectionProps {
    title: string;
    subtitle: string;
    userWelcome?: {
        firstName: string;
        message: string;
    };
}

export default function HeroSection({ title, subtitle, userWelcome }: HeroSectionProps) {
    return (
        <div className="text-center space-y-6">
            <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                    {title}
                </h1>
                <p className="text-xl text-slate-600 max-w-2xl mx-auto">
                    {subtitle}
                </p>
            </div>

            {userWelcome && (
                <Card className="max-w-md mx-auto border-green-200 bg-green-50/50">
                    <CardContent className="pt-6">
                        <p>
                            Welcome back,{" "}
                            <span className="font-semibold">
                                {userWelcome.firstName}
                            </span>
                            ! {userWelcome.message}
                        </p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
