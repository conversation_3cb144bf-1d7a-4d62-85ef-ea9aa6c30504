import React from "react";
import { router, <PERSON> } from "@inertiajs/react";
import { PageProps } from "@/types/inertia";
import * as NavigationMenu from "@radix-ui/react-navigation-menu";
import { Progress } from "@radix-ui/react-progress";
import UserAvatarDropdown from "./UserAvatarDropdown";
import CompetitionSelector from "./CompetitionSelector";
import { Toaster } from "./ui/toaster";
import { cn } from "@/lib/utils";
import { useTenantName } from "@/hooks/useTenant";
import { useFlashMessages } from "@/hooks/useFlashMessages";

export default function Layout({
    auth,
    children,
}: React.PropsWithChildren<PageProps>) {
    const user = auth?.user || null;
    const tenantName = useTenantName();
    const [isNavigating, setIsNavigating] = React.useState(false);
    
    // Handle flash messages
    useFlashMessages();

    React.useEffect(() => {
        const handleStart = () => setIsNavigating(true);
        const handleFinish = () => setIsNavigating(false);

        const removeStartListener = router.on("start", handleStart);
        const removeFinishListener = router.on("finish", handleFinish);

        return () => {
            removeStartListener();
            removeFinishListener();
        };
    }, []);

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-green-50">
            {/* Global Loading Indicator */}
            {isNavigating && (
                <div className="fixed top-0 left-0 right-0 z-50">
                    <Progress
                        value={100}
                        className="h-1 rounded-none bg-blue-200"
                    />
                </div>
            )}

            {/* Navigation */}
            <header className="sticky top-0 z-40 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
                <div className="container mx-auto px-4 flex h-16 items-center justify-between">
                    <div className="flex items-center space-x-8">
                        <Link
                            href="/"
                            className="flex items-center space-x-2 text-xl font-bold text-slate-900"
                        >
                            <span>{tenantName}</span>
                        </Link>

                        <NavigationMenu.Root className="relative z-10 flex max-w-max flex-1 items-center justify-center">
                            <NavigationMenu.List className="group flex flex-1 list-none items-center justify-center space-x-1">
                                <NavigationMenu.Item>
                                    <NavigationMenu.Link asChild>
                                        <Link
                                            href={route("home")}
                                            className={cn(
                                                "group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-slate-100/50 data-[state=open]:bg-slate-100/50"
                                            )}
                                        >
                                            Dashboard
                                        </Link>
                                    </NavigationMenu.Link>
                                </NavigationMenu.Item>
                                <NavigationMenu.Item>
                                    <NavigationMenu.Link asChild>
                                        <Link
                                            href={route("game.team")}
                                            className={cn(
                                                "group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-slate-100/50 data-[state=open]:bg-slate-100/50"
                                            )}
                                        >
                                            My Team
                                        </Link>
                                    </NavigationMenu.Link>
                                </NavigationMenu.Item>
                                <NavigationMenu.Item>
                                    <NavigationMenu.Link asChild>
                                        <Link
                                            href={route("game.transfers")}
                                            className={cn(
                                                "group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-slate-100/50 data-[state=open]:bg-slate-100/50"
                                            )}
                                        >
                                            Transfers
                                        </Link>
                                    </NavigationMenu.Link>
                                </NavigationMenu.Item>
                                <NavigationMenu.Item>
                                    <NavigationMenu.Link asChild>
                                        <Link
                                            href={route("game.leagues")}
                                            className={cn(
                                                "group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-slate-100/50 data-[state=open]:bg-slate-100/50"
                                            )}
                                        >
                                            Leagues
                                        </Link>
                                    </NavigationMenu.Link>
                                </NavigationMenu.Item>
                                <NavigationMenu.Item>
                                    <NavigationMenu.Link asChild>
                                        <Link
                                            href={route("game.fixtures")}
                                            className={cn(
                                                "group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-slate-100/50 data-[state=open]:bg-slate-100/50"
                                            )}
                                        >
                                            Fixtures
                                        </Link>
                                    </NavigationMenu.Link>
                                </NavigationMenu.Item>
                            </NavigationMenu.List>
                        </NavigationMenu.Root>
                    </div>

                    {/* Competition Selector and User Avatar Dropdown */}
                    <div className="flex items-center space-x-4">
                        <CompetitionSelector />
                        {auth?.user && <UserAvatarDropdown auth={auth} />}
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main>{children}</main>

            {/* Toast Notifications */}
            <Toaster />

            {/* Footer */}
            <footer className="border-t">
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center text-slate-600 text-sm space-y-2">
                        <p className="text-slate-500">
                            Fantasy Football - Build your dream team and compete
                            with friends!
                        </p>
                    </div>
                </div>
            </footer>
        </div>
    );
}
