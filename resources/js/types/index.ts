export interface User {
    id: number;
    name: string;
    first_name?: string;
    last_name?: string;
    email: string;
    avatar?: string;
    email_verified_at?: string;
    created_at: string;
    updated_at: string;
}

export interface Auth {
    user: User | null;
}

export interface Flash {
    message?: string;
    error?: string;
    success?: string;
}

export interface PageProps {
    auth: Auth;
    flash: Flash;
}

export interface ErrorProps extends PageProps {
    status: number;
    message: string;
}
