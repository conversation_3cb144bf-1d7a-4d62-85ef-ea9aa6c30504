import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import { PageProps } from "@/types/inertia";
import HeroSection from "../Components/fantasy/HeroSection";
import StatsCard from "../Components/fantasy/StatsCard";
import FantasyNavigationCard from "../Components/fantasy/FantasyNavigationCard";
import ActivityItem from "../Components/fantasy/ActivityItem";
import FixtureItem from "../Components/fantasy/FixtureItem";
import { useTenantName } from "@/hooks/useTenant";

interface DashboardData {
    teamValue: string;
    totalPoints: number;
    globalRank: string;
    currentWeek: string;
    recentActivity: Array<{
        id: number;
        type: string;
        date: string;
        inPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
        outPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
    }>;
    upcomingFixtures: Array<{
        id: number;
        homeTeam: string;
        awayTeam: string;
        kickoff: string;
        gameweek: string;
    }>;
}

interface WelcomeProps extends PageProps {
    dashboardData: DashboardData;
}

export default function Welcome(props: WelcomeProps) {
    const { auth, dashboardData } = props;
    console.log(dashboardData.recentActivity);
    const tenantName = useTenantName();
    const fantasyCards = [
        {
            title: "My Team",
            description: "Manage your squad and formation",
            icon: "👥",
            href: "/my-team",
            color: "bg-green-500 hover:bg-green-600",
        },
        {
            title: "Transfers",
            description: "Buy and sell players",
            icon: "🔄",
            href: "/transfers",
            color: "bg-blue-500 hover:bg-blue-600",
        },
        {
            title: "Leagues",
            description: "Join leagues and compete",
            icon: "🏆",
            href: "/leagues",
            color: "bg-yellow-500 hover:bg-yellow-600",
        },
        {
            title: "Fixtures",
            description: "View upcoming matches",
            icon: "📅",
            href: "/fixtures",
            color: "bg-purple-500 hover:bg-purple-600",
        },
    ];

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Dashboard`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Hero Section */}
                <HeroSection
                    title={`Welcome to ${tenantName}`}
                    subtitle="Build your dream team and compete with friends!"
                    userWelcome={
                        auth?.user?.first_name
                            ? {
                                  firstName: auth.user.first_name,
                                  message: "Ready to manage your team?",
                              }
                            : undefined
                    }
                />

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <StatsCard
                        value={dashboardData.teamValue}
                        label="Team Value"
                    />
                    <StatsCard
                        value={dashboardData.totalPoints.toLocaleString()}
                        label="Total Points"
                    />
                    <StatsCard
                        value={dashboardData.globalRank}
                        label="Global League Rank"
                    />
                    <StatsCard
                        value={dashboardData.currentWeek}
                        label="Current Week"
                    />
                </div>

                {/* Fantasy Navigation Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {fantasyCards.map((card, index) => (
                        <FantasyNavigationCard
                            key={index}
                            title={card.title}
                            description={card.description}
                            icon={card.icon}
                            href={card.href}
                            color={card.color}
                        />
                    ))}
                </div>

                {/* Recent Activity & Upcoming Fixtures */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <span>🔥</span>
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {dashboardData.recentActivity.length > 0 ? (
                                dashboardData.recentActivity.map((activity) => {
                                    let title = "";
                                    let description = "";
                                    let badge = "";
                                    let variant: "green" | "blue" | "amber" =
                                        "blue";

                                    if (
                                        activity.inPlayer &&
                                        activity.outPlayer
                                    ) {
                                        title = `${activity.inPlayer.name} transferred in`;
                                        description = `${activity.outPlayer.name} transferred out`;
                                        badge = "IN";
                                        variant = "blue";
                                    } else if (activity.inPlayer) {
                                        title = `${activity.inPlayer.name} transferred in`;
                                        description = `Cost: £${activity.inPlayer.price.toFixed(
                                            1
                                        )}m`;
                                        badge = "IN";
                                        variant = "blue";
                                    } else if (activity.outPlayer) {
                                        title = `${activity.outPlayer.name} transferred out`;
                                        description = `Transfer date: ${new Date(
                                            activity.date
                                        ).toLocaleDateString()}`;
                                        badge = "OUT";
                                        variant = "amber";
                                    }

                                    return (
                                        <ActivityItem
                                            key={activity.id}
                                            title={title}
                                            description={description}
                                            badge={badge}
                                            variant={variant}
                                        />
                                    );
                                })
                            ) : (
                                <div className="text-center py-4 text-muted-foreground">
                                    No recent activity
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <span>📅</span>
                                Upcoming Fixtures
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {dashboardData.upcomingFixtures.length > 0 ? (
                                dashboardData.upcomingFixtures.map(
                                    (fixture) => (
                                        <FixtureItem
                                            key={fixture.id}
                                            homeTeam={fixture.homeTeam}
                                            awayTeam={fixture.awayTeam}
                                            kickoff={fixture.kickoff}
                                            gameweek={fixture.gameweek}
                                        />
                                    )
                                )
                            ) : (
                                <div className="text-center py-4 text-muted-foreground">
                                    No upcoming fixtures
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Layout>
    );
}
