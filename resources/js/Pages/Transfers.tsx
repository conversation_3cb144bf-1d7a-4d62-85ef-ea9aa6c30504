import { Head, router } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/fantasy/HeroSection";

import { useTenantName } from "@/hooks/useTenant";
import TransferManager from "@/Components/fantasy/TransferManager";

interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

interface TransfersPageProps extends PageProps {
    competition: any;
    season: any;
    gameweek: any;
    fantasyTeam: any;
    availablePlayers: any[];
    currentSquad: any[];
    budget: number;
    freeTransfers: number;
    startingPlayerIds: number[];
}

export default function Transfers(props: TransfersPageProps) {
    const tenantName = useTenantName();
    const { availablePlayers, currentSquad, budget, freeTransfers } = props;

    // Transform backend data to match TransferManager interface
    const transformedAvailablePlayers: Player[] = availablePlayers.map(
        (player: any) => ({
            id: player.id,
            name: player.name,
            position: player.position as "GK" | "DEF" | "MID" | "FWD",
            team: player.team || "Unknown",
            price: player.price || 5.0, // Default price if not set
        })
    );

    // Transform current squad data
    const transformedCurrentSquad: Player[] = currentSquad.map(
        (player: any) => ({
            id: player.id,
            name: player.name,
            position: player.position as "GK" | "DEF" | "MID" | "FWD",
            team: player.team || "Unknown",
            price: player.price || 5.0,
            is_captain: player.is_captain || false,
            is_vice_captain: player.is_vice_captain || false,
        })
    );

    const handleSquadSubmit = (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => {
        // Transform data for backend submission
        const selectedPlayers = squadData.players.map(player => player.id);
        
        // For transfers, assume first 11 players are in lineup
        const lineupPlayers = squadData.players.slice(0, 11).map(player => player.id);
        
        // Calculate transfers made by comparing new squad with original squad
        const originalPlayerIds = currentSquad.map((p: any) => p.id);
        const newPlayerIds = squadData.players.map(p => p.id);
        
        // Find players that are different between the two squads
        const transfersIn = newPlayerIds.filter(id => !originalPlayerIds.includes(id));
        const transfersMade = transfersIn.length;
        
        const submissionData = {
            selected_players: selectedPlayers,
            lineup_players: lineupPlayers,
            captain_id: squadData.captain?.id || null,
            vice_captain_id: squadData.viceCaptain?.id || null,
            transfers_made: transfersMade,
        };

        console.log('Submitting transfer data:', submissionData);

        // Submit to backend
        router.post(route("game.transfers.process"), submissionData, {
            onSuccess: () => {
                console.log('Transfers submission successful');
                // Redirect to home or next step after successful transfers
                router.visit(route("game.team"));
            },
            onError: (errors) => {
                console.error("Transfers submission failed:", errors);
            },
        });
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Transfers`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title="Transfers"
                    subtitle="Buy and sell players to build your dream team"
                />
            </div>
            <TransferManager
                isCreationMode={false}
                initialSquadData={transformedCurrentSquad}
                availablePlayers={transformedAvailablePlayers}
                initialBudget={budget}
                onSquadSubmit={handleSquadSubmit}
            />
        </Layout>
    );
}
