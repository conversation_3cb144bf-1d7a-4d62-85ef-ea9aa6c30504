import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { PageProps } from "../types/inertia";
import { useTenantName } from "../hooks/useTenant";
import HeroSection from "@/Components/fantasy/HeroSection";
import MyTeamManager from "@/Components/fantasy/MyTeamManager";
import StatsCard from "@/Components/fantasy/StatsCard";

interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}

interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
}

interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

interface GamePageProps extends PageProps {
    teamData: TeamData;
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    currentGameweek: CurrentGameweek;
}

export default function MyTeam(props: GamePageProps) {
    const { auth, teamData, squadPlayers, startingPlayerIds, fantasyTeam, currentGameweek } = props;
    const tenantName = useTenantName();

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - My Team`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title="My Team"
                    subtitle="Manage your squad and formation"
                />
                {/* My Team Manager */}
                <MyTeamManager 
                    teamData={teamData}
                    squadPlayers={squadPlayers}
                    startingPlayerIds={startingPlayerIds}
                    fantasyTeam={fantasyTeam}
                    currentGameweek={currentGameweek}
                />
            </div>
        </Layout>
    );
}
