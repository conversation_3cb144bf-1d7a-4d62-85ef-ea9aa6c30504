import { Head, router } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import HeroSection from "../../Components/fantasy/HeroSection";
import { useTenantName } from "@/hooks/useTenant";
import TransferManager from "@/Components/fantasy/TransferManager";

interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

interface SquadPageProps extends PageProps {
    competition: any;
    season: any;
    gameweek: any;
    fantasyTeam: any;
    availablePlayers: any[];
    currentSquad: any[];
    budget: number;
}

export default function Squad(props: SquadPageProps) {
    const tenantName = useTenantName();
    const { availablePlayers, currentSquad, budget } = props;

    // Transform backend data to match TransferManager interface
    const transformedAvailablePlayers: Player[] = availablePlayers.map(
        (player: any) => ({
            id: player.id,
            name: player.name,
            position: player.position as "GK" | "DEF" | "MID" | "FWD",
            team: player.teams?.[0]?.name || "Unknown",
            price: (player.market_value || 5000000) / 1000000, // Convert to millions for display
        })
    );

    // Transform current squad data if exists
    const transformedCurrentSquad: Player[] = currentSquad.map(
        (fantasyPlayer: any) => ({
            id: fantasyPlayer.player.id,
            name: fantasyPlayer.player.name,
            position: fantasyPlayer.player.position as
                | "GK"
                | "DEF"
                | "MID"
                | "FWD",
            team: fantasyPlayer.player.teams?.[0]?.name || "Unknown",
            price: (fantasyPlayer.player.market_value || 5000000) / 1000000, // Convert to millions for display
            is_captain:
                fantasyPlayer.fantasyTeamLineups?.some(
                    (lineup: any) => lineup.is_captain
                ) || false,
            is_vice_captain:
                fantasyPlayer.fantasyTeamLineups?.some(
                    (lineup: any) => lineup.is_vice_captain
                ) || false,
        })
    );

    const handleSquadSubmit = (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => {
        // Transform data for backend submission to match controller expectations
        const selectedPlayers = squadData.players.map(player => player.id);
        
        // For now, assume first 11 players are in lineup (this should be enhanced later)
        const lineupPlayers = squadData.players.slice(0, 11).map(player => player.id);
        
        const submissionData = {
            selected_players: selectedPlayers,
            lineup_players: lineupPlayers,
            captain_id: squadData.captain?.id || null,
            vice_captain_id: squadData.viceCaptain?.id || null,
        };

        console.log('Submitting squad data:', submissionData);

        // Submit to backend
        router.post(route("fantasy-team.squad.store"), submissionData, {
            onSuccess: () => {
                console.log('Squad submission successful');
                // Redirect to home or next step after successful squad creation
                router.visit(route("home"));
            },
            onError: (errors) => {
                console.error("Squad submission failed:", errors);
            },
        });
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Squad`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title="Create Your Squad"
                    subtitle="Build your dream team with the best players"
                />
            </div>
            <TransferManager
                isCreationMode={true}
                initialSquadData={transformedCurrentSquad}
                availablePlayers={transformedAvailablePlayers}
                initialBudget={budget}
                onSquadSubmit={handleSquadSubmit}
            />
        </Layout>
    );
}
