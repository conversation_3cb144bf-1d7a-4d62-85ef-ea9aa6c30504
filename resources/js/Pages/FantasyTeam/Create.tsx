import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import FootballShirtPreview from "../../Components/FootballShirtPreview";
import { <PERSON><PERSON> } from "../../Components/ui/button";
import { Input } from "../../Components/ui/input";
import { Label } from "../../Components/ui/label";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "../../Components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../../Components/ui/select";
import { PageProps } from "../../types/inertia";

interface Competition {
    id: number;
    name: string;
    slug: string;
}

interface Season {
    id: number;
    name: string;
    start_date: string;
    end_date: string;
}

interface ShirtTypeOption {
    value: string;
    label: string;
}

interface CreateFantasyTeamProps extends PageProps {
    competition: Competition;
    season: Season;
    shirtTypes: ShirtTypeOption[];
}

export default function Create({
    competition,
    season,
    shirtTypes,
    ...pageProps
}: CreateFantasyTeamProps) {
    const { data, setData, post, processing, errors } = useForm({
        name: "",
        shirt_type: shirtTypes[0]?.value || "striped",
        shirt_color: "#FF0000",
        strip_color: "#FFFFFF",
    });

    const [selectedShirtType, setSelectedShirtType] = useState(shirtTypes[0]?.value || "striped");

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route("fantasy-team.store"));
    };



    return (
        <Layout {...pageProps}>
            <Head title="Create Fantasy Team" />

            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
                <div className="max-w-2xl mx-auto px-4">
                    <div className="text-center mb-8">
                        <h1 className="text-4xl font-bold text-gray-900 mb-2">
                            Create Your Fantasy Team
                        </h1>
                        <p className="text-lg text-gray-600">
                            Set up your team for {competition.name} -{" "}
                            {season.name}
                        </p>
                    </div>

                    <Card className="shadow-xl">
                        <CardHeader>
                            <CardTitle className="text-2xl text-center">
                                Team Setup
                            </CardTitle>
                            <CardDescription className="text-center">
                                Choose your team name and customize your kit
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-6">
                                {/* Team Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name">Team Name</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) =>
                                            setData("name", e.target.value)
                                        }
                                        placeholder="Enter your team name"
                                        className="text-lg"
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-red-600">
                                            {errors.name}
                                        </p>
                                    )}
                                </div>

                                {/* Kit Customization */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold">
                                        Kit Customization
                                    </h3>

                                    {/* Shirt Type */}
                                    <div className="space-y-2">
                                        <Label htmlFor="shirt_type">
                                            Kit Type
                                        </Label>
                                        <Select
                                            value={data.shirt_type}
                                            onValueChange={(value) => {
                                                setData("shirt_type", value);
                                                setSelectedShirtType(value);
                                            }}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select kit type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {shirtTypes.map((type) => (
                                                    <SelectItem
                                                        key={type.value}
                                                        value={type.value}
                                                    >
                                                        {type.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.shirt_type && (
                                            <p className="text-sm text-red-600">
                                                {errors.shirt_type}
                                            </p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        {/* Shirt Color */}
                                        <div className="space-y-2">
                                            <Label htmlFor="shirt_color">
                                                Primary Color
                                            </Label>
                                            <div className="flex items-center space-x-2">
                                                <Input
                                                    id="shirt_color"
                                                    type="color"
                                                    value={data.shirt_color}
                                                    onChange={(e) =>
                                                        setData(
                                                            "shirt_color",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-16 h-10 p-1 border rounded"
                                                />
                                                <Input
                                                    type="text"
                                                    value={data.shirt_color}
                                                    onChange={(e) =>
                                                        setData(
                                                            "shirt_color",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="flex-1"
                                                    placeholder="#FF0000"
                                                />
                                            </div>
                                            {errors.shirt_color && (
                                                <p className="text-sm text-red-600">
                                                    {errors.shirt_color}
                                                </p>
                                            )}
                                        </div>

                                        {/* Strip Color */}
                                        <div className="space-y-2">
                                            <Label htmlFor="strip_color">
                                                Secondary Color
                                            </Label>
                                            <div className="flex items-center space-x-2">
                                                <Input
                                                    id="strip_color"
                                                    type="color"
                                                    value={data.strip_color}
                                                    onChange={(e) =>
                                                        setData(
                                                            "strip_color",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-16 h-10 p-1 border rounded"
                                                />
                                                <Input
                                                    type="text"
                                                    value={data.strip_color}
                                                    onChange={(e) =>
                                                        setData(
                                                            "strip_color",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="flex-1"
                                                    placeholder="#FFFFFF"
                                                />
                                            </div>
                                            {errors.strip_color && (
                                                <p className="text-sm text-red-600">
                                                    {errors.strip_color}
                                                </p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Kit Preview */}
                                    <div className="space-y-2">
                                        <Label>Kit Preview</Label>
                                        <div className="flex justify-center">
                                            <FootballShirtPreview
                                                shirtType={selectedShirtType as 'striped' | 'filled'}
                                                primaryColor={data.shirt_color}
                                                secondaryColor={data.strip_color}
                                                size="medium"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <div className="pt-6">
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="w-full text-lg py-3"
                                    >
                                        {processing
                                            ? "Creating Team..."
                                            : "Create Fantasy Team"}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Info Card */}
                    <Card className="mt-6">
                        <CardContent className="pt-6">
                            <div className="text-center text-sm text-gray-600">
                                <p className="mb-2">
                                    <strong>Starting Budget:</strong> 100
                                </p>
                                <p>
                                    You can change your team name and kit colors
                                    later in your profile settings.
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Layout>
    );
}
