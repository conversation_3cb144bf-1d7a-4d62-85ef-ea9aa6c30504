import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import { <PERSON><PERSON> } from "@/Components/ui/button";
import { Badge } from "@/Components/ui/badge";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";
import * as Tabs from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/fantasy/HeroSection";
import LeagueCard from "../Components/fantasy/LeagueCard";
import LeaderboardRow from "../Components/fantasy/LeaderboardRow";
import { useTenantName } from "@/hooks/useTenant";

export default function Leagues({ myLeagues, availableLeagues, ...props }: PageProps & { myLeagues: any[], availableLeagues: any[] }) {
    const tenantName = useTenantName();
    const [activeTab, setActiveTab] = useState("my-leagues");

    // Form for creating new league
    const { data: createData, setData: setCreateData, post: createPost, processing: createProcessing, errors: createErrors } = useForm({
        name: '',
        type: 'public',
    });

    // Form for joining by invite code
    const { data: joinData, setData: setJoinData, post: joinPost, processing: joinProcessing, errors: joinErrors } = useForm({
        invite_code: '',
    });

    const handleCreateLeague = (e: React.FormEvent) => {
        e.preventDefault();
        createPost(route('leagues.store'));
    };

    const handleJoinByCode = (e: React.FormEvent) => {
        e.preventDefault();
        joinPost(route('leagues.join-by-code'));
    };

    const handleJoinLeague = (leagueId: number) => {
        createPost(route('leagues.join', leagueId));
    };

    const leagueStandings = [
        {
            rank: 1,
            name: "Alex Johnson",
            teamName: "Thunder Strikers",
            points: 1389,
            lastWeek: 89,
        },
        {
            rank: 2,
            name: "Sarah Wilson",
            teamName: "Goal Diggers",
            points: 1356,
            lastWeek: 67,
        },
        {
            rank: 3,
            name: "You",
            teamName: "Dream Team FC",
            points: 1247,
            lastWeek: 78,
        },
        {
            rank: 4,
            name: "Mike Brown",
            teamName: "Tactical Genius",
            points: 1198,
            lastWeek: 45,
        },
        {
            rank: 5,
            name: "Emma Davis",
            teamName: "Fantasy United",
            points: 1156,
            lastWeek: 82,
        },
        {
            rank: 6,
            name: "Tom Miller",
            teamName: "The Invincibles",
            points: 1134,
            lastWeek: 56,
        },
        {
            rank: 7,
            name: "Lisa Garcia",
            teamName: "Victory Lane",
            points: 1089,
            lastWeek: 73,
        },
        {
            rank: 8,
            name: "James Wilson",
            teamName: "Championship Chasers",
            points: 1067,
            lastWeek: 91,
        },
    ];

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Leagues`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title="Leagues"
                    subtitle="Join leagues and compete with friends"
                />

                {/* Tabs */}
                <div className="flex justify-center">
                    <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
                        <Tabs.List className="grid w-full grid-cols-3 bg-slate-100 rounded-lg p-1 max-w-md">
                            <Tabs.Trigger
                                value="my-leagues"
                                className="px-6 py-2 rounded-md font-medium transition-all data-[state=active]:bg-white data-[state=active]:text-slate-900 data-[state=active]:shadow-sm text-slate-600 hover:text-slate-900"
                            >
                                My Leagues
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="join-league"
                                className="px-6 py-2 rounded-md font-medium transition-all data-[state=active]:bg-white data-[state=active]:text-slate-900 data-[state=active]:shadow-sm text-slate-600 hover:text-slate-900"
                            >
                                Join League
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="create-league"
                                className="px-6 py-2 rounded-md font-medium transition-all data-[state=active]:bg-white data-[state=active]:text-slate-900 data-[state=active]:shadow-sm text-slate-600 hover:text-slate-900"
                            >
                                Create League
                            </Tabs.Trigger>
                        </Tabs.List>
                    </Tabs.Root>
                </div>

                {/* Tab Content */}
                <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
                    <Tabs.Content value="my-leagues" className="space-y-6">
                        {/* My Leagues */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {myLeagues.map((league) => (
                                <LeagueCard
                                    key={league.id}
                                    id={league.id}
                                    name={league.name}
                                    type={league.type as "Private" | "Public"}
                                    members={league.members}
                                    rank={league.rank}
                                    points={league.points}
                                    prize={league.prize}
                                    isJoined={true}
                                />
                            ))}
                        </div>

                        {/* League Standings Example */}
                        <Card>
                            <CardHeader>
                                <CardTitle>
                                    Friends League - Current Standings
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {leagueStandings.map((player) => (
                                        <LeaderboardRow
                                            key={player.rank}
                                            rank={player.rank}
                                            name={player.name}
                                            teamName={player.teamName}
                                            points={player.points}
                                            lastWeek={player.lastWeek}
                                            isCurrentUser={player.name === "You"}
                                        />
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </Tabs.Content>

                    <Tabs.Content value="join-league" className="space-y-6">
                        {/* Join by Code */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Join Private League</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleJoinByCode} className="flex gap-4">
                                    <Input
                                        type="text"
                                        placeholder="Enter league code"
                                        className="flex-1"
                                        value={joinData.invite_code}
                                        onChange={(e) => setJoinData('invite_code', e.target.value)}
                                        required
                                    />
                                    <Button type="submit" disabled={joinProcessing}>
                                        {joinProcessing ? 'Joining...' : 'Join League'}
                                    </Button>
                                </form>
                                {joinErrors.invite_code && (
                                    <p className="text-sm text-red-600 mt-2">{joinErrors.invite_code}</p>
                                )}
                            </CardContent>
                        </Card>

                        {/* Available Public Leagues */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {availableLeagues.map((league) => (
                                <div key={league.id} className="relative">
                                    <LeagueCard
                                        id={league.id}
                                        name={league.name}
                                        type={league.type as "Private" | "Public"}
                                        members={league.members}
                                        entryFee={league.entryFee}
                                        prize={league.prize}
                                        isJoined={false}
                                        onJoin={handleJoinLeague}
                                    />
                                </div>
                            ))}
                        </div>
                    </Tabs.Content>

                    <Tabs.Content value="create-league">
                        <div className="max-w-2xl mx-auto">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-2xl">
                                        Create New League
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleCreateLeague} className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="league-name">
                                                League Name
                                            </Label>
                                            <Input
                                                id="league-name"
                                                placeholder="Enter league name"
                                                value={createData.name}
                                                onChange={(e) => setCreateData('name', e.target.value)}
                                                required
                                            />
                                            {createErrors.name && (
                                                <p className="text-sm text-red-600">{createErrors.name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="league-type">
                                                League Type
                                            </Label>
                                            <Select value={createData.type} onValueChange={(value) => setCreateData('type', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select league type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="private">
                                                        Private (Invite Only)
                                                    </SelectItem>
                                                    <SelectItem value="public">
                                                        Public (Anyone can join)
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {createErrors.type && (
                                                <p className="text-sm text-red-600">{createErrors.type}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm text-slate-600">
                                                League Information
                                            </Label>
                                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm">
                                                <div className="grid grid-cols-1 gap-2">
                                                    <div className="flex justify-between">
                                                        <span className="text-blue-700 font-medium">Organization:</span>
                                                        <span className="text-blue-800">{tenantName}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-blue-700 font-medium">Season:</span>
                                                        <span className="text-blue-800">Current Active Competition</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-blue-700 font-medium">Owner:</span>
                                                        <span className="text-blue-800">You ({props.auth?.user?.name || 'Current User'})</span>
                                                    </div>
                                                    {createData.type === 'private' && (
                                                        <div className="flex justify-between">
                                                            <span className="text-blue-700 font-medium">Invite Code:</span>
                                                            <span className="text-blue-800">Auto-generated</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <Button 
                                            type="submit" 
                                            className="w-full" 
                                            size="lg"
                                            disabled={createProcessing}
                                        >
                                            {createProcessing ? 'Creating...' : 'Create League'}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    </Tabs.Content>
                </Tabs.Root>
            </div>
        </Layout>
    );
}
