import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '../../Components/ui/button';
import { Input } from '../../Components/ui/input';
import { Label } from '../../Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../Components/ui/select';

interface Props {
    phone: string;
    is_new_user: boolean;
}

export default function ProfileCompletion({ phone, is_new_user }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        first_name: '',
        last_name: '',
        date_of_birth: '',
        favorite_team: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('auth.profile.store'));
    };

    const moroccanTeams = [
        'Raja Casablanca',
        'Wydad Casablanca',
        'FAR Rabat',
        'FUS Rabat',
        'Hassania Agadir',
        'Olympique Khouribga',
        'Maghreb Fez',
        'Difaa El Jadida',
        'Renaissance Zemamra',
        'Ittihad Tanger',
        'Moghreb Tétouan',
        'Chabab Mohammedia',
        'Rapide Oued Zem',
        'Union Touarga',
        'Youssoufia Berrechid',
        'Kawkab Marrakech'
    ];

    return (
        <>
            <Head title="Complete Your Profile" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Your Profile</h1>
                        <p className="text-gray-600">
                            {is_new_user ? 'Welcome! Let\'s set up your profile' : 'Please complete your profile to continue'}
                        </p>
                        <p className="text-sm text-gray-500 mt-2">Phone: {phone}</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Personal Information</CardTitle>
                            <CardDescription>
                                Tell us about yourself to personalize your fantasy experience
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="first_name">First Name</Label>
                                        <Input
                                            id="first_name"
                                            type="text"
                                            placeholder="Ahmed"
                                            value={data.first_name}
                                            onChange={(e) => setData('first_name', e.target.value)}
                                            className={errors.first_name ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.first_name && (
                                            <p className="text-sm text-red-600">{errors.first_name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="last_name">Last Name</Label>
                                        <Input
                                            id="last_name"
                                            type="text"
                                            placeholder="Benali"
                                            value={data.last_name}
                                            onChange={(e) => setData('last_name', e.target.value)}
                                            className={errors.last_name ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.last_name && (
                                            <p className="text-sm text-red-600">{errors.last_name}</p>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                                    <Input
                                        id="date_of_birth"
                                        type="date"
                                        value={data.date_of_birth}
                                        onChange={(e) => setData('date_of_birth', e.target.value)}
                                        className={errors.date_of_birth ? 'border-red-500' : ''}
                                        required
                                    />
                                    {errors.date_of_birth && (
                                        <p className="text-sm text-red-600">{errors.date_of_birth}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="favorite_team">Favorite Team</Label>
                                    <Select value={data.favorite_team} onValueChange={(value) => setData('favorite_team', value)}>
                                        <SelectTrigger className={errors.favorite_team ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select your favorite team" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {moroccanTeams.map((team) => (
                                                <SelectItem key={team} value={team}>
                                                    {team}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.favorite_team && (
                                        <p className="text-sm text-red-600">{errors.favorite_team}</p>
                                    )}
                                </div>

                                <Button 
                                    type="submit" 
                                    className="w-full bg-green-600 hover:bg-green-700"
                                    disabled={processing}
                                >
                                    {processing ? 'Saving...' : 'Complete Profile'}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
