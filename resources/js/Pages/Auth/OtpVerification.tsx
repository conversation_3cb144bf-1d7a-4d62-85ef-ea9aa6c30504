import React, { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '../../Components/ui/button';
import { Input } from '../../Components/ui/input';
import { Label } from '../../Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../Components/ui/card';

interface Props {
    phone: string;
    message?: string;
}

export default function OtpVerification({ phone, message }: Props) {
    const [countdown, setCountdown] = useState(60);
    const [canResend, setCanResend] = useState(false);
    
    const { data, setData, post, processing, errors } = useForm({
        otp: '',
    });

    const { post: resendPost, processing: resendProcessing } = useForm();

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    setCanResend(true);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/auth/otp');
    };

    const handleResend = () => {
        resendPost('/auth/otp/resend', {
            onSuccess: () => {
                setCountdown(60);
                setCanResend(false);
            }
        });
    };

    const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setData('otp', value);
    };

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <>
            <Head title="Verify Your Phone" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Verify Your Phone</h1>
                        <p className="text-gray-600">
                            We've sent a 6-digit code to
                        </p>
                        <p className="font-semibold text-gray-900">{phone}</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Enter Verification Code</CardTitle>
                            <CardDescription>
                                {message || 'Please enter the 6-digit code sent to your phone'}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="otp">Verification Code</Label>
                                    <Input
                                        id="otp"
                                        type="text"
                                        placeholder="123456"
                                        value={data.otp}
                                        onChange={handleOtpChange}
                                        className={`text-center text-2xl font-mono tracking-widest ${errors.otp ? 'border-red-500' : ''}`}
                                        maxLength={6}
                                        required
                                    />
                                    {errors.otp && (
                                        <p className="text-sm text-red-600">{errors.otp}</p>
                                    )}
                                </div>

                                <Button 
                                    type="submit" 
                                    className="w-full bg-green-600 hover:bg-green-700"
                                    disabled={processing || data.otp.length !== 6}
                                >
                                    {processing ? 'Verifying...' : 'Verify Code'}
                                </Button>
                            </form>

                            <div className="mt-6 text-center">
                                <p className="text-sm text-gray-600 mb-2">
                                    Didn't receive the code?
                                </p>
                                
                                {canResend ? (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleResend}
                                        disabled={resendProcessing}
                                        className="text-green-600 border-green-600 hover:bg-green-50"
                                    >
                                        {resendProcessing ? 'Sending...' : 'Resend Code'}
                                    </Button>
                                ) : (
                                    <p className="text-sm text-gray-500">
                                        Resend code in {formatTime(countdown)}
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <p className="text-xs text-gray-500">
                            By continuing, you agree to our Terms of Service and Privacy Policy
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
