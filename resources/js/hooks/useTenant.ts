import { usePage } from '@inertiajs/react';
import { PageProps, Tenant } from '@/types/inertia';

/**
 * Custom hook to access current tenant data throughout the application
 * 
 * @returns {Tenant | null} Current tenant data or null if no tenant is active
 */
export function useTenant(): Tenant | null {
    const { tenant } = usePage<PageProps>().props;
    return tenant;
}

/**
 * Custom hook to check if a tenant is currently active
 * 
 * @returns {boolean} True if a tenant is active, false otherwise
 */
export function useHasTenant(): boolean {
    const tenant = useTenant();
    return tenant !== null;
}

/**
 * Custom hook to get tenant name with fallback
 * 
 * @param fallback - Fallback name if no tenant is active
 * @returns {string} Tenant name or fallback
 */
export function useTenantName(fallback: string = 'Fantasy Football'): string {
    const tenant = useTenant();
    return tenant?.name || fallback;
}
